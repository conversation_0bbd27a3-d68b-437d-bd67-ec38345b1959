﻿<!DOCTYPE html>
<html lang="en">

<head>
	<title>Adminty - Premium Admin Template by Colorlib </title>
	<!-- HTML5 Shim and Respond.js IE10 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 10]>
	<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
	<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- Meta -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="#">
	<meta name="keywords" content="Admin , Responsive, Landing, Bootstrap, App, Template, Mobile, iOS, Android, apple, creative app">
	<meta name="author" content="#">
	<!-- Favicon icon -->
	<link rel="icon" href="libraries\assets\images\favicon.ico" type="image/x-icon">
	<!-- Google font-->
	<link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,800" rel="stylesheet">
	<!-- Required Fremwork -->
	<link rel="stylesheet" type="text/css" href="libraries\bower_components\bootstrap\css\bootstrap.min.css">
	<!-- weather-icons -->
	<link rel="stylesheet" type="text/css" href="libraries\assets\icon\weather-icons\css\weather-icons.min.css">
	<link rel="stylesheet" type="text/css" href="libraries\assets\icon\weather-icons\css\weather-icons-wind.min.css">
	<!--SVG Icons Animated-->
	<link rel="stylesheet" type="text/css" href="libraries\assets\icon\SVG-animated\svg-weather.css">
	<!-- themify-icons line icon -->
	<link rel="stylesheet" type="text/css" href="libraries\assets\icon\themify-icons\themify-icons.css">
	<!-- ico font -->
	<link rel="stylesheet" type="text/css" href="libraries\assets\icon\icofont\css\icofont.css">
	<!-- feather Awesome -->
	<link rel="stylesheet" type="text/css" href="libraries\assets\icon\feather\css\feather.css">
	<!-- Style.css -->
	<link rel="stylesheet" type="text/css" href="libraries\assets\css\style.css">
	<link rel="stylesheet" type="text/css" href="libraries\assets\css\jquery.mCustomScrollbar.css">

</head>

<body>
<!-- Pre-loader start -->
<div class="theme-loader">
	<div class="ball-scale">
		<div class='contain'>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
			<div class="ring">
				<div class="frame"></div>
			</div>
		</div>
	</div>
</div>
<!-- Pre-loader end -->
<div id="pcoded" class="pcoded">
	<div class="pcoded-overlay-box"></div>
	<div class="pcoded-container navbar-wrapper">

		<nav class="navbar header-navbar pcoded-header">
			<div class="navbar-wrapper">

				<div class="navbar-logo">
					<a class="mobile-menu" id="mobile-collapse" href="#!">
						<i class="feather icon-menu"></i>
					</a>
					<a href="index-1.htm">
						<img class="img-fluid" src="libraries\assets\images\logo.png" alt="Theme-Logo">
					</a>
					<a class="mobile-options">
						<i class="feather icon-more-horizontal"></i>
					</a>
				</div>

				<div class="navbar-container container-fluid">
					<ul class="nav-left">
						<li class="header-search">
							<div class="main-search morphsearch-search">
								<div class="input-group">
									<span class="input-group-addon search-close"><i class="feather icon-x"></i></span>
									<input type="text" class="form-control">
									<span class="input-group-addon search-btn"><i class="feather icon-search"></i></span>
								</div>
							</div>
						</li>
						<li>
							<a href="#!" onclick="javascript:toggleFullScreen()">
								<i class="feather icon-maximize full-screen"></i>
							</a>
						</li>
					</ul>
					<ul class="nav-right">
						<li class="header-notification">
							<div class="dropdown-primary dropdown">
								<div class="dropdown-toggle" data-toggle="dropdown">
									<i class="feather icon-bell"></i>
									<span class="badge bg-c-pink">5</span>
								</div>
								<ul class="show-notification notification-view dropdown-menu" data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">
									<li>
										<h6>Notifications</h6>
										<label class="label label-danger">New</label>
									</li>
									<li>
										<div class="media">
											<img class="d-flex align-self-center img-radius" src="libraries\assets\images\avatar-4.jpg" alt="Generic placeholder image">
											<div class="media-body">
												<h5 class="notification-user">John Doe</h5>
												<p class="notification-msg">Lorem ipsum dolor sit amet, consectetuer elit.</p>
												<span class="notification-time">30 minutes ago</span>
											</div>
										</div>
									</li>
									<li>
										<div class="media">
											<img class="d-flex align-self-center img-radius" src="libraries\assets\images\avatar-3.jpg" alt="Generic placeholder image">
											<div class="media-body">
												<h5 class="notification-user">Joseph William</h5>
												<p class="notification-msg">Lorem ipsum dolor sit amet, consectetuer elit.</p>
												<span class="notification-time">30 minutes ago</span>
											</div>
										</div>
									</li>
									<li>
										<div class="media">
											<img class="d-flex align-self-center img-radius" src="libraries\assets\images\avatar-4.jpg" alt="Generic placeholder image">
											<div class="media-body">
												<h5 class="notification-user">Sara Soudein</h5>
												<p class="notification-msg">Lorem ipsum dolor sit amet, consectetuer elit.</p>
												<span class="notification-time">30 minutes ago</span>
											</div>
										</div>
									</li>
								</ul>
							</div>
						</li>
						<li class="header-notification">
							<div class="dropdown-primary dropdown">
								<div class="displayChatbox dropdown-toggle" data-toggle="dropdown">
									<i class="feather icon-message-square"></i>
									<span class="badge bg-c-green">3</span>
								</div>
							</div>
						</li>
						<li class="user-profile header-notification">
							<div class="dropdown-primary dropdown">
								<div class="dropdown-toggle" data-toggle="dropdown">
									<img src="libraries\assets\images\avatar-4.jpg" class="img-radius" alt="User-Profile-Image">
									<span>John Doe</span>
									<i class="feather icon-chevron-down"></i>
								</div>
								<ul class="show-notification profile-notification dropdown-menu" data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">
									<li>
										<a href="#!">
											<i class="feather icon-settings"></i> Settings
										</a>
									</li>
									<li>
										<a href="user-profile.htm">
											<i class="feather icon-user"></i> Profile
										</a>
									</li>
									<li>
										<a href="email-inbox.htm">
											<i class="feather icon-mail"></i> My Messages
										</a>
									</li>
									<li>
										<a href="auth-lock-screen.htm">
											<i class="feather icon-lock"></i> Lock Screen
										</a>
									</li>
									<li>
										<a href="auth-normal-sign-in.htm">
											<i class="feather icon-log-out"></i> Logout
										</a>
									</li>
								</ul>

							</div>
						</li>
					</ul>
				</div>
			</div>
		</nav>

		<!-- Sidebar chat start -->
		<div id="sidebar" class="users p-chat-user showChat">
			<div class="had-container">
				<div class="card card_main p-fixed users-main">
					<div class="user-box">
						<div class="chat-inner-header">
							<div class="back_chatBox">
								<div class="right-icon-control">
									<input type="text" class="form-control  search-text" placeholder="Search Friend" id="search-friends">
									<div class="form-icon">
										<i class="icofont icofont-search"></i>
									</div>
								</div>
							</div>
						</div>
						<div class="main-friend-list">
							<div class="media userlist-box" data-id="1" data-status="online" data-username="Josephin Doe" data-toggle="tooltip" data-placement="left" title="Josephin Doe">
								<a class="media-left" href="#!">
									<img class="media-object img-radius img-radius" src="libraries\assets\images\avatar-3.jpg" alt="Generic placeholder image ">
									<div class="live-status bg-success"></div>
								</a>
								<div class="media-body">
									<div class="f-13 chat-header">Josephin Doe</div>
								</div>
							</div>
							<div class="media userlist-box" data-id="2" data-status="online" data-username="Lary Doe" data-toggle="tooltip" data-placement="left" title="Lary Doe">
								<a class="media-left" href="#!">
									<img class="media-object img-radius" src="libraries\assets\images\avatar-2.jpg" alt="Generic placeholder image">
									<div class="live-status bg-success"></div>
								</a>
								<div class="media-body">
									<div class="f-13 chat-header">Lary Doe</div>
								</div>
							</div>
							<div class="media userlist-box" data-id="3" data-status="online" data-username="Alice" data-toggle="tooltip" data-placement="left" title="Alice">
								<a class="media-left" href="#!">
									<img class="media-object img-radius" src="libraries\assets\images\avatar-4.jpg" alt="Generic placeholder image">
									<div class="live-status bg-success"></div>
								</a>
								<div class="media-body">
									<div class="f-13 chat-header">Alice</div>
								</div>
							</div>
							<div class="media userlist-box" data-id="4" data-status="online" data-username="Alia" data-toggle="tooltip" data-placement="left" title="Alia">
								<a class="media-left" href="#!">
									<img class="media-object img-radius" src="libraries\assets\images\avatar-3.jpg" alt="Generic placeholder image">
									<div class="live-status bg-success"></div>
								</a>
								<div class="media-body">
									<div class="f-13 chat-header">Alia</div>
								</div>
							</div>
							<div class="media userlist-box" data-id="5" data-status="online" data-username="Suzen" data-toggle="tooltip" data-placement="left" title="Suzen">
								<a class="media-left" href="#!">
									<img class="media-object img-radius" src="libraries\assets\images\avatar-2.jpg" alt="Generic placeholder image">
									<div class="live-status bg-success"></div>
								</a>
								<div class="media-body">
									<div class="f-13 chat-header">Suzen</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Sidebar inner chat start-->
		<div class="showChat_inner">
			<div class="media chat-inner-header">
				<a class="back_chatBox">
					<i class="feather icon-chevron-left"></i> Josephin Doe
				</a>
			</div>
			<div class="media chat-messages">
				<a class="media-left photo-table" href="#!">
					<img class="media-object img-radius img-radius m-t-5" src="libraries\assets\images\avatar-3.jpg" alt="Generic placeholder image">
				</a>
				<div class="media-body chat-menu-content">
					<div class="">
						<p class="chat-cont">I'm just looking around. Will you tell me something about yourself?</p>
						<p class="chat-time">8:20 a.m.</p>
					</div>
				</div>
			</div>
			<div class="media chat-messages">
				<div class="media-body chat-menu-reply">
					<div class="">
						<p class="chat-cont">I'm just looking around. Will you tell me something about yourself?</p>
						<p class="chat-time">8:20 a.m.</p>
					</div>
				</div>
				<div class="media-right photo-table">
					<a href="#!">
						<img class="media-object img-radius img-radius m-t-5" src="libraries\assets\images\avatar-4.jpg" alt="Generic placeholder image">
					</a>
				</div>
			</div>
			<div class="chat-reply-box p-b-20">
				<div class="right-icon-control">
					<input type="text" class="form-control search-text" placeholder="Share Your Thoughts">
					<div class="form-icon">
						<i class="feather icon-navigation"></i>
					</div>
				</div>
			</div>
		</div>
		<!-- Sidebar inner chat end-->
		<div class="pcoded-main-container">
			<div class="pcoded-wrapper">
				<nav class="pcoded-navbar">
					<div class="pcoded-inner-navbar main-menu">
						<div class="pcoded-navigatio-lavel">Navigation</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-home"></i></span>
									<span class="pcoded-mtext">Dashboard</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="index-1.htm">
											<span class="pcoded-mtext">Default</span>
										</a>
									</li>
									<li class="">
										<a href="dashboard-crm.htm">
											<span class="pcoded-mtext">CRM</span>
										</a>
									</li>
									<li class=" ">
										<a href="dashboard-analytics.htm">
											<span class="pcoded-mtext">Analytics</span>
											<span class="pcoded-badge label label-info ">NEW</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-sidebar"></i></span>
									<span class="pcoded-mtext">Page layouts</span>
									<span class="pcoded-badge label label-warning">NEW</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" pcoded-hasmenu">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Vertical</span>
										</a>
										<ul class="pcoded-submenu">
											<li class=" ">
												<a href="menu-static.htm">
													<span class="pcoded-mtext">Static Layout</span>
												</a>
											</li>
											<li class=" ">
												<a href="menu-header-fixed.htm">
													<span class="pcoded-mtext">Header Fixed</span>
												</a>
											</li>
											<li class=" ">
												<a href="menu-compact.htm">
													<span class="pcoded-mtext">Compact</span>
												</a>
											</li>
											<li class=" ">
												<a href="menu-sidebar.htm">
													<span class="pcoded-mtext">Sidebar Fixed</span>
												</a>
											</li>

										</ul>
									</li>
									<li class=" pcoded-hasmenu">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Horizontal</span>
										</a>
										<ul class="pcoded-submenu">
											<li class=" ">
												<a href="menu-horizontal-static.htm" target="_blank">
													<span class="pcoded-mtext">Static Layout</span>
												</a>
											</li>
											<li class=" ">
												<a href="menu-horizontal-fixed.htm" target="_blank">
													<span class="pcoded-mtext">Fixed layout</span>
												</a>
											</li>
											<li class=" ">
												<a href="menu-horizontal-icon.htm" target="_blank">
													<span class="pcoded-mtext">Static With Icon</span>
												</a>
											</li>
											<li class=" ">
												<a href="menu-horizontal-icon-fixed.htm" target="_blank">
													<span class="pcoded-mtext">Fixed With Icon</span>
												</a>
											</li>
										</ul>
									</li>
									<li class=" ">
										<a href="menu-bottom.htm">
											<span class="pcoded-mtext">Bottom Menu</span>
										</a>
									</li>
									<li class=" ">
										<a href="box-layout.htm" target="_blank">
											<span class="pcoded-mtext">Box Layout</span>
										</a>
									</li>
									<li class=" ">
										<a href="menu-rtl.htm" target="_blank">
											<span class="pcoded-mtext">RTL</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="">
								<a href="navbar-light.htm">
									<span class="pcoded-micon"><i class="feather icon-menu"></i></span>
									<span class="pcoded-mtext">Navigation</span>
								</a>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-layers"></i></span>
									<span class="pcoded-mtext">Widget</span>
									<span class="pcoded-badge label label-danger">100+</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="widget-statistic.htm">
											<span class="pcoded-mtext">Statistic</span>
										</a>
									</li>
									<li class=" ">
										<a href="widget-data.htm">
											<span class="pcoded-mtext">Data</span>
										</a>
									</li>
									<li class="">
										<a href="widget-chart.htm">
											<span class="pcoded-mtext">Chart Widget</span>
										</a>
									</li>

								</ul>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">UI Element</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-box"></i></span>
									<span class="pcoded-mtext">Basic Components</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="alert.htm">
											<span class="pcoded-mtext">Alert</span>
										</a>
									</li>
									<li class=" ">
										<a href="breadcrumb.htm">
											<span class="pcoded-mtext">Breadcrumbs</span>
										</a>
									</li>
									<li class=" ">
										<a href="button.htm">
											<span class="pcoded-mtext">Button</span>
										</a>
									</li>
									<li class=" ">
										<a href="box-shadow.htm">
											<span class="pcoded-mtext">Box-Shadow</span>
										</a>
									</li>
									<li class=" ">
										<a href="accordion.htm">
											<span class="pcoded-mtext">Accordion</span>
										</a>
									</li>
									<li class=" ">
										<a href="generic-class.htm">
											<span class="pcoded-mtext">Generic Class</span>
										</a>
									</li>
									<li class=" ">
										<a href="tabs.htm">
											<span class="pcoded-mtext">Tabs</span>
										</a>
									</li>
									<li class=" ">
										<a href="color.htm">
											<span class="pcoded-mtext">Color</span>
										</a>
									</li>
									<li class=" ">
										<a href="label-badge.htm">
											<span class="pcoded-mtext">Label Badge</span>
										</a>
									</li>
									<li class=" ">
										<a href="progress-bar.htm">
											<span class="pcoded-mtext">Progress Bar</span>
										</a>
									</li>
									<li class=" ">
										<a href="preloader.htm">
											<span class="pcoded-mtext">Pre-Loader</span>
										</a>
									</li>
									<li class=" ">
										<a href="list.htm">
											<span class="pcoded-mtext">List</span>
										</a>
									</li>
									<li class=" ">
										<a href="tooltip.htm">
											<span class="pcoded-mtext">Tooltip And Popover</span>
										</a>
									</li>
									<li class=" ">
										<a href="typography.htm">
											<span class="pcoded-mtext">Typography</span>
										</a>
									</li>
									<li class=" ">
										<a href="other.htm">
											<span class="pcoded-mtext">Other</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-gitlab"></i></span>
									<span class="pcoded-mtext">Advance Components</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="draggable.htm">
											<span class="pcoded-mtext">Draggable</span>
										</a>
									</li>
									<li class=" ">
										<a href="bs-grid.htm">
											<span class="pcoded-mtext">Grid Stack</span>
										</a>
									</li>
									<li class=" ">
										<a href="light-box.htm">
											<span class="pcoded-mtext">Light Box</span>
										</a>
									</li>
									<li class=" ">
										<a href="modal.htm">
											<span class="pcoded-mtext">Modal</span>
										</a>
									</li>
									<li class=" ">
										<a href="notification.htm">
											<span class="pcoded-mtext">Notifications</span>
										</a>
									</li>
									<li class=" ">
										<a href="notify.htm">
											<span class="pcoded-mtext">PNOTIFY</span>
											<span class="pcoded-badge label label-info">NEW</span>
										</a>
									</li>
									<li class=" ">
										<a href="rating.htm">
											<span class="pcoded-mtext">Rating</span>
										</a>
									</li>
									<li class=" ">
										<a href="range-slider.htm">
											<span class="pcoded-mtext">Range Slider</span>
										</a>
									</li>
									<li class=" ">
										<a href="slider.htm">
											<span class="pcoded-mtext">Slider</span>
										</a>
									</li>
									<li class=" ">
										<a href="syntax-highlighter.htm">
											<span class="pcoded-mtext">Syntax Highlighter</span>
										</a>
									</li>
									<li class=" ">
										<a href="tour.htm">
											<span class="pcoded-mtext">Tour</span>
										</a>
									</li>
									<li class=" ">
										<a href="treeview.htm">
											<span class="pcoded-mtext">Tree View</span>
										</a>
									</li>
									<li class=" ">
										<a href="nestable.htm">
											<span class="pcoded-mtext">Nestable</span>
										</a>
									</li>
									<li class=" ">
										<a href="toolbar.htm">
											<span class="pcoded-mtext">Toolbar</span>
										</a>
									</li>
									<li class=" ">
										<a href="x-editable.htm">
											<span class="pcoded-mtext">X-Editable</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-package"></i></span>
									<span class="pcoded-mtext">Extra Components</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="session-timeout.htm">
											<span class="pcoded-mtext">Session Timeout</span>
										</a>
									</li>
									<li class=" ">
										<a href="session-idle-timeout.htm">
											<span class="pcoded-mtext">Session Idle Timeout</span>
										</a>
									</li>
									<li class=" ">
										<a href="offline.htm">
											<span class="pcoded-mtext">Offline</span>
										</a>
									</li>

								</ul>
							</li>
							<li class=" ">
								<a href="animation.htm">
									<span class="pcoded-micon"><i class="feather icon-aperture rotate-refresh"></i><b>A</b></span>
									<span class="pcoded-mtext">Animations</span>
								</a>
							</li>
							<li class=" ">
								<a href="sticky.htm">
									<span class="pcoded-micon"><i class="feather icon-cpu"></i></span>
									<span class="pcoded-mtext">Sticky Notes</span>
									<span class="pcoded-badge label label-danger">HOT</span>
								</a>
							</li>
							<li class="pcoded-hasmenu pcoded-trigger">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-command"></i></span>
									<span class="pcoded-mtext">Icons</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="icon-font-awesome.htm">
											<span class="pcoded-mtext">Font Awesome</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-themify.htm">
											<span class="pcoded-mtext">Themify</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-simple-line.htm">
											<span class="pcoded-mtext">Simple Line Icon</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-ion.htm">
											<span class="pcoded-mtext">Ion Icon</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-material-design.htm">
											<span class="pcoded-mtext">Material Design</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-icofonts.htm">
											<span class="pcoded-mtext">Ico Fonts</span>
										</a>
									</li>
									<li class="active">
										<a href="icon-weather.htm">
											<span class="pcoded-mtext">Weather Icon</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-typicons.htm">
											<span class="pcoded-mtext">Typicons</span>
										</a>
									</li>
									<li class=" ">
										<a href="icon-flags.htm">
											<span class="pcoded-mtext">Flags</span>
										</a>
									</li>
								</ul>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Forms</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-clipboard"></i></span>
									<span class="pcoded-mtext">Form Components</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="form-elements-component.htm">
											<span class="pcoded-mtext">Form Components</span>
										</a>
									</li>
									<li class=" ">
										<a href="form-elements-add-on.htm">
											<span class="pcoded-mtext">Form-Elements-Add-On</span>
										</a>
									</li>
									<li class=" ">
										<a href="form-elements-advance.htm">
											<span class="pcoded-mtext">Form-Elements-Advance</span>
										</a>
									</li>
									<li class=" ">
										<a href="form-validation.htm">
											<span class="pcoded-mtext">Form Validation</span>
										</a>
									</li>
								</ul>
							</li>
							<li class=" ">
								<a href="form-picker.htm">
									<span class="pcoded-micon"><i class="feather icon-edit-1"></i></span>
									<span class="pcoded-mtext">Form Picker</span>
									<span class="pcoded-badge label label-warning">NEW</span>
								</a>
							</li>
							<li class=" ">
								<a href="form-select.htm">
									<span class="pcoded-micon"><i class="feather icon-feather"></i></span>
									<span class="pcoded-mtext">Form Select</span>
								</a>
							</li>
							<li class=" ">
								<a href="form-masking.htm">
									<span class="pcoded-micon"><i class="feather icon-shield"></i></span>
									<span class="pcoded-mtext">Form Masking</span>
								</a>
							</li>
							<li class=" ">
								<a href="form-wizard.htm">
									<span class="pcoded-micon"><i class="feather icon-tv"></i></span>
									<span class="pcoded-mtext">Form Wizard</span>
								</a>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-book"></i></span>
									<span class="pcoded-mtext">Ready To Use</span>
									<span class="pcoded-badge label label-danger">HOT</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="ready-cloned-elements-form.htm">
											<span class="pcoded-mtext">Cloned Elements Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-currency-form.htm">
											<span class="pcoded-mtext">Currency Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-form-booking.htm">
											<span class="pcoded-mtext">Booking Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-form-booking-multi-steps.htm">
											<span class="pcoded-mtext">Booking Multi Steps Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-form-comment.htm">
											<span class="pcoded-mtext">Comment Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-form-contact.htm">
											<span class="pcoded-mtext">Contact Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-job-application-form.htm">
											<span class="pcoded-mtext">Job Application Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-js-addition-form.htm">
											<span class="pcoded-mtext">JS Addition Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-login-form.htm">
											<span class="pcoded-mtext">Login Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-popup-modal-form.htm" target="_blank">
											<span class="pcoded-mtext">Popup Modal Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-registration-form.htm">
											<span class="pcoded-mtext">Registration Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-review-form.htm">
											<span class="pcoded-mtext">Review Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-subscribe-form.htm">
											<span class="pcoded-mtext">Subscribe Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-suggestion-form.htm">
											<span class="pcoded-mtext">Suggestion Form</span>
										</a>
									</li>
									<li class=" ">
										<a href="ready-tabs-form.htm">
											<span class="pcoded-mtext">Tabs Form</span>
										</a>
									</li>
								</ul>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Tables</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-credit-card"></i></span>
									<span class="pcoded-mtext">Bootstrap Table</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="bs-basic-table.htm">
											<span class="pcoded-mtext">Basic Table</span>
										</a>
									</li>
									<li class=" ">
										<a href="bs-table-sizing.htm">
											<span class="pcoded-mtext">Sizing Table</span>
										</a>
									</li>
									<li class=" ">
										<a href="bs-table-border.htm">
											<span class="pcoded-mtext">Border Table</span>
										</a>
									</li>
									<li class=" ">
										<a href="bs-table-styling.htm">
											<span class="pcoded-mtext">Styling Table</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-inbox"></i></span>
									<span class="pcoded-mtext">Data Table</span>
									<span class="pcoded-mcaret"></span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="dt-basic.htm">
											<span class="pcoded-mtext">Basic Initialization</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-advance.htm">
											<span class="pcoded-mtext">Advance Initialization</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-styling.htm">
											<span class="pcoded-mtext">Styling</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-api.htm">
											<span class="pcoded-mtext">API</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ajax.htm">
											<span class="pcoded-mtext">Ajax</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-server-side.htm">
											<span class="pcoded-mtext">Server Side</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-plugin.htm">
											<span class="pcoded-mtext">Plug-In</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-data-sources.htm">
											<span class="pcoded-mtext">Data Sources</span>
										</a>
									</li>

								</ul>
							</li>
							<li class="pcoded-hasmenu">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-server"></i></span>
									<span class="pcoded-mtext">Data Table Extensions</span>
								</a>
								<ul class="pcoded-submenu">
									<li class=" ">
										<a href="dt-ext-autofill.htm">
											<span class="pcoded-mtext">AutoFill</span>
										</a>
									</li>
									<li class="pcoded-hasmenu">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Button</span>
										</a>
										<ul class="pcoded-submenu">
											<li class=" ">
												<a href="dt-ext-basic-buttons.htm">
													<span class="pcoded-mtext">Basic Button</span>
												</a>
											</li>
											<li class=" ">
												<a href="dt-ext-buttons-html-5-data-export.htm">
													<span class="pcoded-mtext">Html-5 Data Export</span>
												</a>
											</li>
										</ul>
									</li>
									<li class=" ">
										<a href="dt-ext-col-reorder.htm">
											<span class="pcoded-mtext">Col Reorder</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-fixed-columns.htm">
											<span class="pcoded-mtext">Fixed Columns</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-fixed-header.htm">
											<span class="pcoded-mtext">Fixed Header</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-key-table.htm">
											<span class="pcoded-mtext">Key Table</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-responsive.htm">
											<span class="pcoded-mtext">Responsive</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-row-reorder.htm">
											<span class="pcoded-mtext">Row Reorder</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-scroller.htm">
											<span class="pcoded-mtext">Scroller</span>
										</a>
									</li>
									<li class=" ">
										<a href="dt-ext-select.htm">
											<span class="pcoded-mtext">Select Table</span>
										</a>
									</li>
								</ul>
							</li>
							<li class=" ">
								<a href="foo-table.htm">
									<span class="pcoded-micon"><i class="feather icon-hash"></i></span>
									<span class="pcoded-mtext">FooTable</span>
								</a>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-airplay"></i></span>
									<span class="pcoded-mtext">Handson Table</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="handson-appearance.htm">
											<span class="pcoded-mtext">Appearance</span>
										</a>
									</li>
									<li class="">
										<a href="handson-data-operation.htm">
											<span class="pcoded-mtext">Data Operation</span>
										</a>
									</li>
									<li class="">
										<a href="handson-rows-cols.htm">
											<span class="pcoded-mtext">Rows Columns</span>
										</a>
									</li>
									<li class="">
										<a href="handson-columns-only.htm">
											<span class="pcoded-mtext">Columns Only</span>
										</a>
									</li>
									<li class="">
										<a href="handson-cell-features.htm">
											<span class="pcoded-mtext">Cell Features</span>
										</a>
									</li>
									<li class="">
										<a href="handson-cell-types.htm">
											<span class="pcoded-mtext">Cell Types</span>
										</a>
									</li>
									<li class="">
										<a href="handson-integrations.htm">
											<span class="pcoded-mtext">Integrations</span>
										</a>
									</li>
									<li class="">
										<a href="handson-rows-only.htm">
											<span class="pcoded-mtext">Rows Only</span>
										</a>
									</li>
									<li class="">
										<a href="handson-utilities.htm">
											<span class="pcoded-mtext">Utilities</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="">
								<a href="editable-table.htm">
									<span class="pcoded-micon"><i class="feather icon-edit"></i></span>
									<span class="pcoded-mtext">Editable Table</span>
								</a>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Chart And Maps</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-pie-chart"></i></span>
									<span class="pcoded-mtext">Charts</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="chart-google.htm">
											<span class="pcoded-mtext">Google Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-echart.htm">
											<span class="pcoded-mtext">Echarts</span>
										</a>
									</li>
									<li class="">
										<a href="chart-chartjs.htm">
											<span class="pcoded-mtext">ChartJs</span>
										</a>
									</li>
									<li class="">
										<a href="chart-list.htm">
											<span class="pcoded-mtext">List Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-float.htm">
											<span class="pcoded-mtext">Float Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-knob.htm">
											<span class="pcoded-mtext">Knob chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-morris.htm">
											<span class="pcoded-mtext">Morris Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-nvd3.htm">
											<span class="pcoded-mtext">Nvd3 Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-peity.htm">
											<span class="pcoded-mtext">Peity Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-radial.htm">
											<span class="pcoded-mtext">Radial Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-rickshaw.htm">
											<span class="pcoded-mtext">Rickshaw Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-sparkline.htm">
											<span class="pcoded-mtext">Sparkline Chart</span>
										</a>
									</li>
									<li class="">
										<a href="chart-c3.htm">
											<span class="pcoded-mtext">C3 Chart</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-map"></i></span>
									<span class="pcoded-mtext">Maps</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="map-google.htm">
											<span class="pcoded-mtext">Google Maps</span>
										</a>
									</li>
									<li class="">
										<a href="map-vector.htm">
											<span class="pcoded-mtext">Vector Maps</span>
										</a>
									</li>
									<li class="">
										<a href="map-api.htm">
											<span class="pcoded-mtext">Google Map Search API</span>
										</a>
									</li>
									<li class="">
										<a href="location.htm">
											<span class="pcoded-mtext">Location</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="">
								<a href="libraries\extra-pages\landingpage\index.htm" target="_blank">
									<span class="pcoded-micon"><i class="feather icon-navigation-2"></i></span>
									<span class="pcoded-mtext">Landing Page</span>
								</a>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Pages</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-unlock"></i></span>
									<span class="pcoded-mtext">Authentication</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="auth-normal-sign-in.htm" target="_blank">
											<span class="pcoded-mtext">Login With BG Image</span>
										</a>
									</li>
									<li class="">
										<a href="auth-sign-in-social.htm" target="_blank">
											<span class="pcoded-mtext">Login With Social Icon</span>
										</a>
									</li>
									<li class="">
										<a href="auth-sign-in-social-header-footer.htm" target="_blank">
											<span class="pcoded-mtext">Login Social With Header And Footer</span>
										</a>
									</li>
									<li class="">
										<a href="auth-normal-sign-in-header-footer.htm" target="_blank">
											<span class="pcoded-mtext">Login With Header And Footer</span>
										</a>
									</li>
									<li class="">
										<a href="auth-sign-up.htm" target="_blank">
											<span class="pcoded-mtext">Registration BG Image</span>
										</a>
									</li>
									<li class="">
										<a href="auth-sign-up-social.htm" target="_blank">
											<span class="pcoded-mtext">Registration Social Icon</span>
										</a>
									</li>
									<li class="">
										<a href="auth-sign-up-social-header-footer.htm" target="_blank">
											<span class="pcoded-mtext">Registration Social With Header And Footer</span>
										</a>
									</li>
									<li class="">
										<a href="auth-sign-up-header-footer.htm" target="_blank">
											<span class="pcoded-mtext">Registration With Header And Footer</span>
										</a>
									</li>
									<li class="">
										<a href="auth-multi-step-sign-up.htm" target="_blank">
											<span class="pcoded-mtext">Multi Step Registration</span>
										</a>
									</li>
									<li class="">
										<a href="auth-reset-password.htm" target="_blank">
											<span class="pcoded-mtext">Forgot Password</span>
										</a>
									</li>
									<li class="">
										<a href="auth-lock-screen.htm" target="_blank">
											<span class="pcoded-mtext">Lock Screen</span>
										</a>
									</li>
									<li class="">
										<a href="auth-modal.htm" target="_blank">
											<span class="pcoded-mtext">Modal</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-sliders"></i></span>
									<span class="pcoded-mtext">Maintenance</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="error.htm">
											<span class="pcoded-mtext">Error</span>
										</a>
									</li>
									<li class="">
										<a href="comming-soon.htm">
											<span class="pcoded-mtext">Comming Soon</span>
										</a>
									</li>
									<li class="">
										<a href="offline-ui.htm">
											<span class="pcoded-mtext">Offline UI</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-users"></i></span>
									<span class="pcoded-mtext">User Profile</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="timeline.htm">
											<span class="pcoded-mtext">Timeline</span>
										</a>
									</li>
									<li class="">
										<a href="timeline-social.htm">
											<span class="pcoded-mtext">Timeline Social</span>
										</a>
									</li>
									<li class="">
										<a href="user-profile.htm">
											<span class="pcoded-mtext">User Profile</span>
										</a>
									</li>
									<li class="">
										<a href="user-card.htm">
											<span class="pcoded-mtext">User Card</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-shopping-cart"></i></span>
									<span class="pcoded-mtext">E-Commerce</span>
									<span class="pcoded-badge label label-danger">NEW</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="product.htm">
											<span class="pcoded-mtext">Product</span>
										</a>
									</li>
									<li class="">
										<a href="product-list.htm">
											<span class="pcoded-mtext">Product List</span>
										</a>
									</li>
									<li class="">
										<a href="product-edit.htm">
											<span class="pcoded-mtext">Product Edit</span>
										</a>
									</li>
									<li class="">
										<a href="product-detail.htm">
											<span class="pcoded-mtext">Product Detail</span>
										</a>
									</li>
									<li class="">
										<a href="product-cart.htm">
											<span class="pcoded-mtext">Product Card</span>
										</a>
									</li>
									<li class="">
										<a href="product-payment.htm">
											<span class="pcoded-mtext">Credit Card Form</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-mail"></i></span>
									<span class="pcoded-mtext">Email</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="email-compose.htm">
											<span class="pcoded-mtext">Compose Email</span>
										</a>
									</li>
									<li class="">
										<a href="email-inbox.htm">
											<span class="pcoded-mtext">Inbox</span>
										</a>
									</li>
									<li class="">
										<a href="email-read.htm">
											<span class="pcoded-mtext">Read Mail</span>
										</a>
									</li>
									<li class="pcoded-hasmenu ">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Email Template</span>
										</a>
										<ul class="pcoded-submenu">
											<li class="">
												<a href="libraries\extra-pages\email-templates\email-welcome.htm">
													<span class="pcoded-mtext">Welcome Email</span>
												</a>
											</li>
											<li class="">
												<a href="libraries\extra-pages\email-templates\email-password.htm">
													<span class="pcoded-mtext">Reset Password</span>
												</a>
											</li>
											<li class="">
												<a href="libraries\extra-pages\email-templates\email-newsletter.htm">
													<span class="pcoded-mtext">Newsletter Email</span>
												</a>
											</li>
											<li class="">
												<a href="libraries\extra-pages\email-templates\email-launch.htm">
													<span class="pcoded-mtext">App Launch</span>
												</a>
											</li>
											<li class="">
												<a href="libraries\extra-pages\email-templates\email-activation.htm">
													<span class="pcoded-mtext">Activation Code</span>
												</a>
											</li>
										</ul>
									</li>
								</ul>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">App</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class=" ">
								<a href="chat.htm">
									<span class="pcoded-micon"><i class="feather icon-message-square"></i></span>
									<span class="pcoded-mtext">Chat</span>
								</a>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-globe"></i></span>
									<span class="pcoded-mtext">Social</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="fb-wall.htm">
											<span class="pcoded-mtext">Wall</span>
										</a>
									</li>
									<li class="">
										<a href="message.htm">
											<span class="pcoded-mtext">Messages</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-check-circle"></i></span>
									<span class="pcoded-mtext">Task</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="task-list.htm">
											<span class="pcoded-mtext">Task List</span>
										</a>
									</li>
									<li class="">
										<a href="task-board.htm">
											<span class="pcoded-mtext">Task Board</span>
										</a>
									</li>
									<li class="">
										<a href="task-detail.htm">
											<span class="pcoded-mtext">Task Detail</span>
										</a>
									</li>
									<li class="">
										<a href="issue-list.htm">
											<span class="pcoded-mtext">Issue List</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-bookmark"></i></span>
									<span class="pcoded-mtext">To-Do</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="todo.htm">
											<span class="pcoded-mtext">To-Do</span>
										</a>
									</li>
									<li class="">
										<a href="notes.htm">
											<span class="pcoded-mtext">Notes</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-image"></i></span>
									<span class="pcoded-mtext">Gallery</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="gallery-grid.htm">
											<span class="pcoded-mtext">Gallery-Grid</span>
										</a>
									</li>
									<li class="">
										<a href="gallery-masonry.htm">
											<span class="pcoded-mtext">Masonry Gallery</span>
										</a>
									</li>
									<li class="">
										<a href="gallery-advance.htm">
											<span class="pcoded-mtext">Advance Gallery</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-search"></i><b>S</b></span>
									<span class="pcoded-mtext">Search</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="search-result.htm">
											<span class="pcoded-mtext">Simple Search</span>
										</a>
									</li>
									<li class="">
										<a href="search-result2.htm">
											<span class="pcoded-mtext">Grouping Search</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-award"></i></span>
									<span class="pcoded-mtext">Job Search</span>
									<span class="pcoded-badge label label-danger">NEW</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="job-card-view.htm">
											<span class="pcoded-mtext">Card View</span>
										</a>
									</li>
									<li class="">
										<a href="job-details.htm">
											<span class="pcoded-mtext">Job Detailed</span>
										</a>
									</li>
									<li class="">
										<a href="job-find.htm">
											<span class="pcoded-mtext">Job Find</span>
										</a>
									</li>
									<li class="">
										<a href="job-panel-view.htm">
											<span class="pcoded-mtext">Job Panel View</span>
										</a>
									</li>
								</ul>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Extension</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-file-plus"></i></span>
									<span class="pcoded-mtext">Editor</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="ck-editor.htm">
											<span class="pcoded-mtext">CK-Editor</span>
										</a>
									</li>
									<li class="">
										<a href="wysiwyg-editor.htm">
											<span class="pcoded-mtext">WYSIWYG Editor</span>
										</a>
									</li>
									<li class="">
										<a href="ace-editor.htm">
											<span class="pcoded-mtext">Ace Editor</span>
										</a>
									</li>
									<li class="">
										<a href="long-press-editor.htm">
											<span class="pcoded-mtext">Long Press Editor</span>
										</a>
									</li>
								</ul>
							</li>
						</ul>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-file-minus"></i></span>
									<span class="pcoded-mtext">Invoice</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="invoice.htm">
											<span class="pcoded-mtext">Invoice</span>
										</a>
									</li>
									<li class="">
										<a href="invoice-summary.htm">
											<span class="pcoded-mtext">Invoice Summary</span>
										</a>
									</li>
									<li class="">
										<a href="invoice-list.htm">
											<span class="pcoded-mtext">Invoice List</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-calendar"></i></span>
									<span class="pcoded-mtext">Event Calendar</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="event-full-calender.htm">
											<span class="pcoded-mtext">Full Calendar</span>
										</a>
									</li>
									<li class="">
										<a href="event-clndr.htm">
											<span class="pcoded-mtext">CLNDER</span>
											<span class="pcoded-badge label label-info">NEW</span>
										</a>
									</li>
								</ul>
							</li>
							<li class="">
								<a href="image-crop.htm">
									<span class="pcoded-micon"><i class="feather icon-scissors"></i></span>
									<span class="pcoded-mtext">Image Cropper</span>
								</a>
							</li>
							<li class="">
								<a href="file-upload.htm">
									<span class="pcoded-micon"><i class="feather icon-upload-cloud"></i></span>
									<span class="pcoded-mtext">File Upload</span>
								</a>
							</li>
							<li class="">
								<a href="change-loges.htm">
									<span class="pcoded-micon"><i class="feather icon-briefcase"></i><b>CL</b></span>
									<span class="pcoded-mtext">Change Loges</span>
								</a>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Other</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="pcoded-hasmenu ">
								<a href="javascript:void(0)">
									<span class="pcoded-micon"><i class="feather icon-list"></i></span>
									<span class="pcoded-mtext">Menu Levels</span>
								</a>
								<ul class="pcoded-submenu">
									<li class="">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Menu Level 2.1</span>
										</a>
									</li>
									<li class="pcoded-hasmenu ">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Menu Level 2.2</span>
										</a>
										<ul class="pcoded-submenu">
											<li class="">
												<a href="javascript:void(0)">
													<span class="pcoded-mtext">Menu Level 3.1</span>
												</a>
											</li>
										</ul>
									</li>
									<li class="">
										<a href="javascript:void(0)">
											<span class="pcoded-mtext">Menu Level 2.3</span>
										</a>
									</li>

								</ul>
							</li>
							<li class="">
								<a href="javascript:void(0)" class="disabled">
									<span class="pcoded-micon"><i class="feather icon-power"></i></span>
									<span class="pcoded-mtext">Disabled Menu</span>
								</a>
							</li>
							<li class="">
								<a href="sample-page.htm">
									<span class="pcoded-micon"><i class="feather icon-watch"></i></span>
									<span class="pcoded-mtext">Sample Page</span>
								</a>
							</li>
						</ul>
						<div class="pcoded-navigatio-lavel">Support</div>
						<ul class="pcoded-item pcoded-left-item">
							<li class="">
								<a href="http://html.codedthemes.com/Adminty/doc" target="_blank">
									<span class="pcoded-micon"><i class="feather icon-monitor"></i></span>
									<span class="pcoded-mtext">Documentation</span>
								</a>
							</li>
							<li class="">
								<a href="#" target="_blank">
									<span class="pcoded-micon"><i class="feather icon-help-circle"></i></span>
									<span class="pcoded-mtext">Submit Issue</span>
								</a>
							</li>
						</ul>
					</div>
				</nav>
				<div class="pcoded-content">
					<div class="pcoded-inner-content">
						<!-- Main-body start -->
						<div class="main-body">
							<div class="page-wrapper">
								<!-- Page-header start -->
								<div class="page-header">
									<div class="row align-items-end">
										<div class="col-lg-8">
											<div class="page-header-title">
												<div class="d-inline">
													<h4>Weather-icons</h4>
													<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>
												</div>
											</div>
										</div>
										<div class="col-lg-4">
											<div class="page-header-breadcrumb">
												<ul class="breadcrumb-title">
													<li class="breadcrumb-item">
														<a href="index-1.htm"> <i class="feather icon-home"></i> </a>
													</li>
													<li class="breadcrumb-item"><a href="#!">Icons</a>
													</li>
													<li class="breadcrumb-item"><a href="#">Weather-icons</a>
													</li>
												</ul>
											</div>
										</div>
									</div>
								</div>
								<!-- Page-header end -->

									<!-- Page body start -->
									<div class="page-body">
										<div class="row">
											<div class="col-sm-12">
												<!-- SVG Icons Animated card start -->
												<div class="card">
													<div class="card-header">
														<h5>SVG Icons Animated<label class="label bg-success m-l-10"> New </label> </h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-svg-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sun" viewbox="15 15 70 70">
																	<clippath>
																		<path d="M0,0v100h100V0H0z M50.001,57.999c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C57.999,54.417,54.418,57.999,50.001,57.999z"></path>
																	</clippath>
																	<g class="climacon_iconWrap climacon_iconWrap-sun">
																		<g class="climacon_componentWrap climacon_componentWrap-sun">
																			<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M72.03,51.999h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S73.136,51.999,72.03,51.999z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northEast" d="M64.175,38.688c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L64.175,38.688z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M50.034,34.002c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C52.034,33.106,51.136,34.002,50.034,34.002z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northWest" d="M35.893,38.688l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C37.94,39.469,36.674,39.469,35.893,38.688z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-west" d="M34.034,50c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C33.14,48,34.034,48.896,34.034,50z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-southWest" d="M35.893,61.312c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L35.893,61.312z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-south" d="M50.034,65.998c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C48.034,66.893,48.929,65.998,50.034,65.998z"></path>
																				<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-southEast" d="M64.175,61.312l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C62.126,60.531,63.392,60.531,64.175,61.312z"></path>
																			</g>
																			<g class="climacon_componentWrap climacon_componentWrap_sunBody" clip-path="url(#sunFillClip)">
																				<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="50.034" cy="50" r="11.999"></circle>
																			</g>
																		</g>
																	</g>
																</svg> Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-sunFill">
													<g class="climacon_componentWrap climacon_componentWrap-sun">
														<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M72.03,51.999h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S73.136,51.999,72.03,51.999z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northEast" d="M64.175,38.688c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L64.175,38.688z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M50.034,34.002c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C52.034,33.106,51.136,34.002,50.034,34.002z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northWest" d="M35.893,38.688l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C37.94,39.469,36.674,39.469,35.893,38.688z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-west" d="M34.034,50c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C33.14,48,34.034,48.896,34.034,50z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-southWest" d="M35.893,61.312c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L35.893,61.312z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-south" d="M50.034,65.998c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C48.034,66.893,48.929,65.998,50.034,65.998z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-southEast" d="M64.175,61.312l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C62.126,60.531,63.392,60.531,64.175,61.312z"></path>
														</g>
														<g class="climacon_componentWrap climacon_componentWrap_sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="50.034" cy="50" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="50.034" cy="50" r="7.999"></circle>
														</g>
													</g>
												</g>
											</svg> Sun Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_moon" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M50,57.999c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C56.979,55.344,53.802,57.999,50,57.999z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-moon">
													<g class="climacon_componentWrap climacon_componentWrap-moon" clip-path="url(#moonFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_moon" d="M50,61.998c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C61.998,56.626,56.626,61.998,50,61.998z"></path>
													</g>
												</g>
											</svg> Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_moonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-moonFill">
													<g class="climacon_componentWrap climacon_componentWrap-moon">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_moon" d="M50,61.998c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C61.998,56.626,56.626,61.998,50,61.998z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M48.212,42.208c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C52.938,50.884,49.115,47.062,48.212,42.208z"></path>
													</g>
												</g>
											</svg> MoonFill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_snowflake" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M50,54c-2.209,0-4-1.791-4-4c0-2.208,1.791-3.999,4-3.999s3.999,1.791,3.999,3.999C53.999,52.209,52.209,54,50,54z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-snowflake">
													<g class="climacon_componentWrap climacon_componentWrap-snowflake" clip-path="url(#snowflakeFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowflake" d="M59.659,46.733l-1.958,1.13c0.188,0.682,0.298,1.396,0.298,2.137c0,0.742-0.108,1.456-0.298,2.139l1.958,1.129c0.956,0.553,1.284,1.775,0.731,2.732c-0.553,0.956-1.774,1.284-2.731,0.73l-1.954-1.127c-1.003,1.02-2.277,1.766-3.705,2.133v2.263c0,1.104-0.896,2-2,2c-1.104,0-2-0.896-2-2v-2.263c-1.428-0.367-2.703-1.113-3.705-2.133l-1.954,1.127c-0.957,0.554-2.18,0.226-2.731-0.73c-0.553-0.957-0.225-2.18,0.731-2.732l1.958-1.129c-0.189-0.683-0.298-1.396-0.298-2.139c0-0.741,0.108-1.455,0.298-2.137l-1.958-1.13c-0.956-0.553-1.284-1.775-0.731-2.732c0.552-0.956,1.774-1.284,2.731-0.731l1.954,1.128c1.002-1.02,2.277-1.766,3.705-2.134v-2.262c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.262c1.428,0.368,2.702,1.114,3.705,2.134l1.954-1.128c0.957-0.553,2.18-0.225,2.731,0.731C60.943,44.958,60.615,46.181,59.659,46.733z"></path>
													</g>
												</g>
											</svg> Snow Flake
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_snowflakeFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-snowflakeFill">
													<g class="climacon_componentWrap climacon_componentWrap-snowflake">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowflake" d="M59.659,46.733l-1.958,1.13c0.188,0.682,0.298,1.396,0.298,2.137c0,0.742-0.108,1.456-0.298,2.139l1.958,1.129c0.956,0.553,1.284,1.775,0.731,2.732c-0.553,0.956-1.774,1.284-2.731,0.73l-1.954-1.127c-1.003,1.02-2.277,1.766-3.705,2.133v2.263c0,1.104-0.896,2-2,2c-1.104,0-2-0.896-2-2v-2.263c-1.428-0.367-2.703-1.113-3.705-2.133l-1.954,1.127c-0.957,0.554-2.18,0.226-2.731-0.73c-0.553-0.957-0.225-2.18,0.731-2.732l1.958-1.129c-0.189-0.683-0.298-1.396-0.298-2.139c0-0.741,0.108-1.455,0.298-2.137l-1.958-1.13c-0.956-0.553-1.284-1.775-0.731-2.732c0.552-0.956,1.774-1.284,2.731-0.731l1.954,1.128c1.002-1.02,2.277-1.766,3.705-2.134v-2.262c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.262c1.428,0.368,2.702,1.114,3.705,2.134l1.954-1.128c0.957-0.553,2.18-0.225,2.731,0.731C60.943,44.958,60.615,46.181,59.659,46.733z"></path>
														<circle class="climacon_component climacon_component-fill climacon_component-fill_snowflake" fill="#FFFFFF" cx="50" cy="50" r="4"></circle>
													</g>
												</g>
											</svg> Snow Flake Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_wind" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-wind">
													<g class="climacon_wrapperComponent climacon_componentWrap-wind">
														<path class="climacon_component climacon_component-stroke climacon_component-wind climacon_component-wind_curl" d="M65.999,52L65.999,52h-3c-1.104,0-2-0.895-2-1.999c0-1.104,0.896-2,2-2h3c1.104,0,2-0.896,2-1.999c0-1.105-0.896-2-2-2s-2-0.896-2-2s0.896-2,2-2c0.138,0,0.271,0.014,0.401,0.041c3.121,0.211,5.597,2.783,5.597,5.959C71.997,49.314,69.312,52,65.999,52z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-wind" d="M55.999,48.001h-2h-6.998H34.002c-1.104,0-1.999,0.896-1.999,2c0,1.104,0.895,1.999,1.999,1.999h2h3.999h3h4h3h3.998h2c3.313,0,6,2.688,6,6c0,3.176-2.476,5.748-5.597,5.959C56.271,63.986,56.139,64,55.999,64c-1.104,0-2-0.896-2-2c0-1.105,0.896-2,2-2s2-0.896,2-2s-0.896-2-2-2h-2h-3.998h-3h-4h-3h-3.999h-2c-3.313,0-5.999-2.686-5.999-5.999c0-3.175,2.475-5.747,5.596-5.959c0.131-0.026,0.266-0.04,0.403-0.04l0,0h12.999h6.998h2c1.104,0,2-0.896,2-2s-0.896-2-2-2s-2-0.895-2-2c0-1.104,0.896-2,2-2c0.14,0,0.272,0.015,0.403,0.041c3.121,0.211,5.597,2.783,5.597,5.959C61.999,45.314,59.312,48.001,55.999,48.001z"></path>
													</g>
												</g>
											</svg> Wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_tornado" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-tornado">
													<g class="climacon_componentWrap climacon_componentWrap-tornado">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_tornadoLine" d="M68.997,36.459H31.002c-1.104,0-2-0.896-2-1.999c0-1.104,0.896-2,2-2h37.995c1.104,0,2,0.896,2,2C70.997,35.563,70.102,36.459,68.997,36.459z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_tornadoLine" d="M35.002,40.459h29.996c1.104,0,2,0.896,2,2s-0.896,1.999-2,1.999H35.002c-1.104,0-2-0.896-2-1.999C33.002,41.354,33.898,40.459,35.002,40.459z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_tornadoLine" d="M39.001,48.458h21.998c1.104,0,1.999,0.896,1.999,1.999c0,1.104-0.896,2-1.999,2H39.001c-1.104,0-1.999-0.896-1.999-2C37.002,49.354,37.897,48.458,39.001,48.458z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_tornadoLine" d="M47,64.456h5.999c1.104,0,2,0.896,2,1.999s-0.896,2-2,2H47c-1.104,0-2-0.896-2-2S45.896,64.456,47,64.456z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_tornadoLine" d="M40.869,58.456c0-1.104,0.896-1.999,2-1.999h13.998c1.104,0,2,0.896,2,1.999c0,1.104-0.896,2-2,2H42.869C41.765,60.456,40.869,59.561,40.869,58.456z"></path>
													</g>
												</g>
											</svg> Tornado
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloud" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloud">
													<g class="climacon_componentWrap climacon_componentWrap_cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
													</g>
												</g>
											</svg> Cloud
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloud">
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_cloudSun-iconWrap">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-orth" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M44.033,65.641c-8.836,0-15.999-7.162-15.999-15.998c0-8.835,7.163-15.998,15.999-15.998c6.006,0,11.233,3.312,13.969,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.26,65.641,47.23,65.641,44.033,65.641z"></path>
													</g>
												</g>
											</svg>Cloud Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSunFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_cloudSunFill-iconWrap">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M44.033,65.641c-8.836,0-15.999-7.162-15.999-15.998c0-8.835,7.163-15.998,15.999-15.998c6.006,0,11.233,3.312,13.969,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.26,65.641,47.23,65.641,44.033,65.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M60.035,61.641c4.418,0,8-3.582,8-7.998c0-4.418-3.582-8-8-8c-1.6,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.976-9.29-11.668-9.29c-6.627,0-11.999,5.372-11.999,11.999c0,6.627,5.372,11.998,11.999,11.998C47.65,61.641,57.016,61.641,60.035,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Sun Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_moon" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M44.033,65.641c-8.836,0-15.999-7.162-15.999-15.998c0-8.835,7.163-15.998,15.999-15.998c6.006,0,11.233,3.312,13.969,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.26,65.641,47.23,65.641,44.033,65.641z"></path>
													</g>
												</g>
											</svg> Cloud Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudMoonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudMoonFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_moon" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M44.033,65.641c-8.836,0-15.999-7.162-15.999-15.998c0-8.835,7.163-15.998,15.999-15.998c6.006,0,11.233,3.312,13.969,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.26,65.641,47.23,65.641,44.033,65.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M60.035,61.641c4.418,0,8-3.582,8-7.998c0-4.418-3.582-8-8-8c-1.6,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.976-9.29-11.668-9.29c-6.627,0-11.999,5.372-11.999,11.999c0,6.627,5.372,11.998,11.999,11.998C47.65,61.641,57.016,61.641,60.035,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzle" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzle">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M42.001,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2.001-0.895-2.001-2v-3.998C40,54.538,40.896,53.644,42.001,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M49.999,53.644c1.104,0,2,0.896,2,2v4c0,1.104-0.896,2-2,2s-1.998-0.896-1.998-2v-4C48.001,54.54,48.896,53.644,49.999,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M57.999,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2-0.895-2-2v-3.998C55.999,54.538,56.894,53.644,57.999,53.644z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.944v-4.381c2.387-1.386,3.998-3.961,3.998-6.92c0-4.418-3.58-8-7.998-8c-1.603,0-3.084,0.481-4.334,1.291c-1.232-5.316-5.973-9.29-11.664-9.29c-6.628,0-11.999,5.372-11.999,12c0,3.549,1.55,6.729,3.998,8.926v4.914c-4.776-2.769-7.998-7.922-7.998-13.84c0-8.836,7.162-15.999,15.999-15.999c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.336-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12C71.997,58.864,68.655,63.296,63.999,64.944z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M42.001,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2.001-0.895-2.001-2v-3.998C40,54.538,40.896,53.644,42.001,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M49.999,53.644c1.104,0,2,0.896,2,2v4c0,1.104-0.896,2-2,2s-1.998-0.896-1.998-2v-4C48.001,54.54,48.896,53.644,49.999,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M57.999,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2-0.895-2-2v-3.998C55.999,54.538,56.894,53.644,57.999,53.644z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleSun">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M42.001,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2.001-0.895-2.001-2v-3.998C40,54.538,40.896,53.644,42.001,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M49.999,53.644c1.104,0,2,0.896,2,2v4c0,1.104-0.896,2-2,2s-1.998-0.896-1.998-2v-4C48.001,54.54,48.896,53.644,49.999,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M57.999,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2-0.895-2-2v-3.998C55.999,54.538,56.894,53.644,57.999,53.644z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.944v-4.381c2.387-1.386,3.998-3.961,3.998-6.92c0-4.418-3.58-8-7.998-8c-1.603,0-3.084,0.481-4.334,1.291c-1.232-5.316-5.973-9.29-11.664-9.29c-6.628,0-11.999,5.372-11.999,12c0,3.549,1.55,6.729,3.998,8.926v4.914c-4.776-2.769-7.998-7.922-7.998-13.84c0-8.836,7.162-15.999,15.999-15.999c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.336-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12C71.997,58.864,68.655,63.296,63.999,64.944z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_moon" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M42.001,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2.001-0.895-2.001-2v-3.998C40,54.538,40.896,53.644,42.001,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M49.999,53.644c1.104,0,2,0.896,2,2v4c0,1.104-0.896,2-2,2s-1.998-0.896-1.998-2v-4C48.001,54.54,48.896,53.644,49.999,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M57.999,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2-0.895-2-2v-3.998C55.999,54.538,56.894,53.644,57.999,53.644z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.944v-4.381c2.387-1.386,3.998-3.961,3.998-6.92c0-4.418-3.58-8-7.998-8c-1.603,0-3.084,0.481-4.334,1.291c-1.232-5.316-5.973-9.29-11.664-9.29c-6.628,0-11.999,5.372-11.999,12c0,3.549,1.55,6.729,3.998,8.926v4.914c-4.776-2.769-7.998-7.922-7.998-13.84c0-8.836,7.162-15.999,15.999-15.999c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.336-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12C71.997,58.864,68.655,63.296,63.999,64.944z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleMoonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleMoonFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_moon" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M42.001,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2.001-0.895-2.001-2v-3.998C40,54.538,40.896,53.644,42.001,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M49.999,53.644c1.104,0,2,0.896,2,2v4c0,1.104-0.896,2-2,2s-1.998-0.896-1.998-2v-4C48.001,54.54,48.896,53.644,49.999,53.644z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M57.999,53.644c1.104,0,2,0.896,2,2v3.998c0,1.105-0.896,2-2,2c-1.105,0-2-0.895-2-2v-3.998C55.999,54.538,56.894,53.644,57.999,53.644z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleAlt" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M56.969,57.672l-2.121,2.121c-1.172,1.172-1.172,3.072,0,4.242c1.17,1.172,3.07,1.172,4.24,0c1.172-1.17,1.172-3.07,0-4.242L56.969,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M50.088,57.672l-2.119,2.121c-1.174,1.172-1.174,3.07,0,4.242c1.17,1.172,3.068,1.172,4.24,0s1.172-3.07,0-4.242L50.088,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M43.033,57.672l-2.121,2.121c-1.172,1.172-1.172,3.07,0,4.242s3.07,1.172,4.244,0c1.172-1.172,1.172-3.07,0-4.242L43.033,57.672z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.943,41.642c-0.696,0-1.369,0.092-2.033,0.205c-2.736-4.892-7.961-8.203-13.965-8.203c-8.835,0-15.998,7.162-15.998,15.997c0,5.992,3.3,11.207,8.177,13.947c0.276-1.262,0.892-2.465,1.873-3.445l0.057-0.057c-3.644-2.061-6.106-5.963-6.106-10.445c0-6.626,5.372-11.998,11.998-11.998c5.691,0,10.433,3.974,11.666,9.29c1.25-0.81,2.732-1.291,4.332-1.291c4.418,0,8,3.581,8,7.999c0,3.443-2.182,6.371-5.235,7.498c0.788,1.146,1.194,2.471,1.222,3.807c4.666-1.645,8.014-6.077,8.014-11.305C71.941,47.014,66.57,41.642,59.943,41.642z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleFillAlt" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleFillAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M56.969,57.672l-2.121,2.121c-1.172,1.172-1.172,3.072,0,4.242c1.17,1.172,3.07,1.172,4.24,0c1.172-1.17,1.172-3.07,0-4.242L56.969,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M50.088,57.672l-2.119,2.121c-1.174,1.172-1.174,3.07,0,4.242c1.17,1.172,3.068,1.172,4.24,0s1.172-3.07,0-4.242L50.088,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M43.033,57.672l-2.121,2.121c-1.172,1.172-1.172,3.07,0,4.242s3.07,1.172,4.244,0c1.172-1.172,1.172-3.07,0-4.242L43.033,57.672z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Fill Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleSunAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleSunAlt">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M56.969,57.672l-2.121,2.121c-1.172,1.172-1.172,3.072,0,4.242c1.17,1.172,3.07,1.172,4.24,0c1.172-1.17,1.172-3.07,0-4.242L56.969,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M50.088,57.672l-2.119,2.121c-1.174,1.172-1.174,3.07,0,4.242c1.17,1.172,3.068,1.172,4.24,0s1.172-3.07,0-4.242L50.088,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M43.033,57.672l-2.121,2.121c-1.172,1.172-1.172,3.07,0,4.242s3.07,1.172,4.244,0c1.172-1.172,1.172-3.07,0-4.242L43.033,57.672z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.944v-4.381c2.387-1.386,3.998-3.961,3.998-6.92c0-4.418-3.58-8-7.998-8c-1.603,0-3.084,0.481-4.334,1.291c-1.232-5.316-5.973-9.29-11.664-9.29c-6.628,0-11.999,5.372-11.999,12c0,3.549,1.55,6.729,3.998,8.926v4.914c-4.776-2.769-7.998-7.922-7.998-13.84c0-8.836,7.162-15.999,15.999-15.999c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.336-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12C71.997,58.864,68.655,63.296,63.999,64.944z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Sun Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleSunFillAlt" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleSunFillAlt">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M56.969,57.672l-2.121,2.121c-1.172,1.172-1.172,3.072,0,4.242c1.17,1.172,3.07,1.172,4.24,0c1.172-1.17,1.172-3.07,0-4.242L56.969,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M50.088,57.672l-2.119,2.121c-1.174,1.172-1.174,3.07,0,4.242c1.17,1.172,3.068,1.172,4.24,0s1.172-3.07,0-4.242L50.088,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M43.033,57.672l-2.121,2.121c-1.172,1.172-1.172,3.07,0,4.242s3.07,1.172,4.244,0c1.172-1.172,1.172-3.07,0-4.242L43.033,57.672z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Sun Fill Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleMoonAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleMoonAlt">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M56.969,57.672l-2.121,2.121c-1.172,1.172-1.172,3.072,0,4.242c1.17,1.172,3.07,1.172,4.24,0c1.172-1.17,1.172-3.07,0-4.242L56.969,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M50.088,57.672l-2.119,2.121c-1.174,1.172-1.174,3.07,0,4.242c1.17,1.172,3.068,1.172,4.24,0s1.172-3.07,0-4.242L50.088,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M43.033,57.672l-2.121,2.121c-1.172,1.172-1.172,3.07,0,4.242s3.07,1.172,4.244,0c1.172-1.172,1.172-3.07,0-4.242L43.033,57.672z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.943,41.642c-0.696,0-1.369,0.092-2.033,0.205c-2.736-4.892-7.961-8.203-13.965-8.203c-8.835,0-15.998,7.162-15.998,15.997c0,5.992,3.3,11.207,8.177,13.947c0.276-1.262,0.892-2.465,1.873-3.445l0.057-0.057c-3.644-2.061-6.106-5.963-6.106-10.445c0-6.626,5.372-11.998,11.998-11.998c5.691,0,10.433,3.974,11.666,9.29c1.25-0.81,2.732-1.291,4.332-1.291c4.418,0,8,3.581,8,7.999c0,3.443-2.182,6.371-5.235,7.498c0.788,1.146,1.194,2.471,1.222,3.807c4.666-1.645,8.014-6.077,8.014-11.305C71.941,47.014,66.57,41.642,59.943,41.642z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Moon Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudDrizzleMoonFillAlt" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudDrizzleMoonFillAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-drizzle">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-left" d="M56.969,57.672l-2.121,2.121c-1.172,1.172-1.172,3.072,0,4.242c1.17,1.172,3.07,1.172,4.24,0c1.172-1.17,1.172-3.07,0-4.242L56.969,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-middle" d="M50.088,57.672l-2.119,2.121c-1.174,1.172-1.174,3.07,0,4.242c1.17,1.172,3.068,1.172,4.24,0s1.172-3.07,0-4.242L50.088,57.672z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_drizzle climacon_component-stroke_drizzle-right" d="M43.033,57.672l-2.121,2.121c-1.172,1.172-1.172,3.07,0,4.242s3.07,1.172,4.244,0c1.172-1.172,1.172-3.07,0-4.242L43.033,57.672z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Drizzle Moon Fill Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRain" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudRain">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent_cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.943,64.941v-4.381c2.389-1.384,4-3.961,4-6.92c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.48-4.334,1.291c-1.23-5.317-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.998c0,3.549,1.551,6.728,4,8.924v4.916c-4.777-2.768-8-7.922-8-13.84c0-8.835,7.163-15.997,15.998-15.997c6.004,0,11.229,3.311,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.372,11.998,12C71.941,58.863,68.602,63.293,63.943,64.941z"></path>
													</g>
												</g>
											</svg> Cloud Rain
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRainFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Rain Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRainSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainSun">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.943,64.941v-4.381c2.389-1.384,4-3.961,4-6.92c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.48-4.334,1.291c-1.23-5.317-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.998c0,3.549,1.551,6.728,4,8.924v4.916c-4.777-2.768-8-7.922-8-13.84c0-8.835,7.163-15.997,15.998-15.997c6.004,0,11.229,3.311,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.372,11.998,12C71.941,58.863,68.602,63.293,63.943,64.941z"></path>
													</g>
												</g>
											</svg> Cloud Rain Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRainSunFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainSunFill">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Rain Sun Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRainMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.943,41.642c-0.696,0-1.369,0.092-2.033,0.205c-2.736-4.892-7.961-8.203-13.965-8.203c-8.835,0-15.998,7.162-15.998,15.997c0,5.992,3.3,11.207,8.177,13.947c0.276-1.262,0.892-2.465,1.873-3.445l0.057-0.057c-3.644-2.061-6.106-5.963-6.106-10.445c0-6.626,5.372-11.998,11.998-11.998c5.691,0,10.433,3.974,11.666,9.29c1.25-0.81,2.732-1.291,4.332-1.291c4.418,0,8,3.581,8,7.999c0,3.443-2.182,6.371-5.235,7.498c0.788,1.146,1.194,2.471,1.222,3.807c4.666-1.645,8.014-6.077,8.014-11.305C71.941,47.014,66.57,41.642,59.943,41.642z"></path>
													</g>
												</g>
											</svg> Cloud Rain Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRainMoonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainMoonFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- left" d="M41.946,53.641c1.104,0,1.999,0.896,1.999,2v15.998c0,1.105-0.895,2-1.999,2s-2-0.895-2-2V55.641C39.946,54.537,40.842,53.641,41.946,53.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- middle" d="M49.945,57.641c1.104,0,2,0.896,2,2v15.998c0,1.104-0.896,2-2,2s-2-0.896-2-2V59.641C47.945,58.535,48.841,57.641,49.945,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- right" d="M57.943,53.641c1.104,0,2,0.896,2,2v15.998c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2V55.641C55.943,54.537,56.84,53.641,57.943,53.641z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Rain Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	Cloud Snow Sun Alt Cloud Rain Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudRainAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainAltFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain climacon_wrapperComponent-rain_alt">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Rain Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSunRainAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainSunAlt">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain climacon_wrapperComponent-rain_alt">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,65.641c-0.267,0-0.614,0-1,0c0-1.373-0.319-2.742-0.942-4c0.776,0,1.45,0,1.942,0c4.418,0,7.999-3.58,7.999-7.998c0-4.418-3.581-8-7.999-8c-1.601,0-3.083,0.481-4.334,1.29c-1.231-5.316-5.973-9.289-11.664-9.289c-6.627,0-11.998,5.372-11.998,11.998c0,5.953,4.339,10.879,10.023,11.822c-0.637,1.218-0.969,2.55-1.012,3.888c-7.406-1.399-13.012-7.896-13.012-15.709c0-8.835,7.162-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.204c0.664-0.114,1.337-0.205,2.033-0.205c6.627,0,11.998,5.372,11.998,12C71.996,60.27,66.626,65.641,59.999,65.641z"></path>
													</g>
												</g>
											</svg> Cloud Rain Sun Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSunRainAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainSunAltFill">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain climacon_wrapperComponent-rain_alt">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Rain Sun Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudMoonRainAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainMoonAlt">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain climacon_wrapperComponent-rain_alt">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent_cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,65.641c-0.267,0-0.614,0-1,0c0-1.373-0.319-2.742-0.942-4c0.776,0,1.45,0,1.942,0c4.418,0,7.999-3.58,7.999-7.998c0-4.418-3.581-8-7.999-8c-1.601,0-3.083,0.481-4.334,1.29c-1.231-5.316-5.973-9.289-11.664-9.289c-6.627,0-11.998,5.372-11.998,11.998c0,5.953,4.339,10.879,10.023,11.822c-0.637,1.218-0.969,2.55-1.012,3.888c-7.406-1.399-13.012-7.896-13.012-15.709c0-8.835,7.162-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.204c0.664-0.114,1.337-0.205,2.033-0.205c6.627,0,11.998,5.372,11.998,12C71.996,60.27,66.626,65.641,59.999,65.641z"></path>
													</g>
												</g>
											</svg> Cloud Rain Moon Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudMoonRainAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudRainSunAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-rain climacon_wrapperComponent-rain_alt">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_rain climacon_component-stroke_rain- alt" d="M50.001,58.568l3.535,3.535c1.95,1.953,1.95,5.119,0,7.07c-1.953,1.953-5.119,1.953-7.07,0c-1.953-1.951-1.953-5.117,0-7.07L50.001,58.568z"></path>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Rain Moon Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudHailAlt" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudHailAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-hailAlt">
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.941v-4.381c2.39-1.384,3.999-3.961,3.999-6.92c0-4.417-3.581-8-7.998-8c-1.602,0-3.084,0.48-4.334,1.291c-1.23-5.317-5.974-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.998c0,3.549,1.55,6.728,3.999,8.924v4.916c-4.776-2.768-7.998-7.922-7.998-13.84c0-8.835,7.162-15.997,15.997-15.997c6.004,0,11.229,3.311,13.966,8.203c0.663-0.113,1.336-0.205,2.033-0.205c6.626,0,11.998,5.372,11.998,12C71.998,58.863,68.656,63.293,63.999,64.941z"></path>
													</g>
												</g>
											</svg> Cloud Hail Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudHailAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudHailAltFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-hailAlt">
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Hail Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudHailAltSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudHailAltSun">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-hailAlt">
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.941v-4.381c2.39-1.384,3.999-3.961,3.999-6.92c0-4.417-3.581-8-7.998-8c-1.602,0-3.084,0.48-4.334,1.291c-1.23-5.317-5.974-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.998c0,3.549,1.55,6.728,3.999,8.924v4.916c-4.776-2.768-7.998-7.922-7.998-13.84c0-8.835,7.162-15.997,15.997-15.997c6.004,0,11.229,3.311,13.966,8.203c0.663-0.113,1.336-0.205,2.033-0.205c6.626,0,11.998,5.372,11.998,12C71.998,58.863,68.656,63.293,63.999,64.941z"></path>
													</g>
												</g>
											</svg> Cloud Hail Alt Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudHailAltSun" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudHailAltSun">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-hailAlt">
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Hail Alt Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudHailAltMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudHailAltMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
															<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-hailAlt">
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud" clip-path="url(#cloudFillClip)">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.941v-4.381c2.39-1.384,3.999-3.961,3.999-6.92c0-4.417-3.581-8-7.998-8c-1.602,0-3.084,0.48-4.334,1.291c-1.23-5.317-5.974-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.998c0,3.549,1.55,6.728,3.999,8.924v4.916c-4.776-2.768-7.998-7.922-7.998-13.84c0-8.835,7.162-15.997,15.997-15.997c6.004,0,11.229,3.311,13.966,8.203c0.663-0.113,1.336-0.205,2.033-0.205c6.626,0,11.998,5.372,11.998,12C71.998,58.863,68.656,63.293,63.999,64.941z"></path>
													</g>
												</g>
											</svg> Cloud Hail Alt Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudHailAltMoonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudHailAltMoon">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-hailAlt">
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-left">
															<circle cx="42" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-middle">
															<circle cx="49.999" cy="65.498" r="2"></circle>
														</g>
														<g class="climacon_component climacon_component-stroke climacon_component-stroke_hailAlt climacon_component-stroke_hailAlt-right">
															<circle cx="57.998" cy="65.498" r="2"></circle>
														</g>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Hail Alt Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnow" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnow">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snow" clip-path="url(#snowFillClip)">
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-left" cx="42.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-middle" cx="50.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-right" cx="57.999" cy="59.641" r="2"></circle>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.943v-4.381c2.39-1.386,3.999-3.963,3.999-6.922c0-4.417-3.581-7.999-7.999-7.999c-1.601,0-3.083,0.48-4.333,1.291c-1.23-5.317-5.974-9.291-11.665-9.291c-6.627,0-11.998,5.373-11.998,12c0,3.549,1.55,6.729,4,8.924v4.916c-4.777-2.769-8-7.922-8-13.84c0-8.836,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c6.627,0,11.999,5.373,11.999,11.999C71.998,58.863,68.654,63.293,63.999,64.943z"></path>
													</g>
												</g>
											</svg> Cloud Snow
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snow">
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-left" cx="42.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-middle" cx="50.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-right" cx="57.999" cy="59.641" r="2"></circle>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Snow Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowSun">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snow">
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-left" cx="42.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-middle" cx="50.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-right" cx="57.999" cy="59.641" r="2"></circle>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.943v-4.381c2.39-1.386,3.999-3.963,3.999-6.922c0-4.417-3.581-7.999-7.999-7.999c-1.601,0-3.083,0.48-4.333,1.291c-1.23-5.317-5.974-9.291-11.665-9.291c-6.627,0-11.998,5.373-11.998,12c0,3.549,1.55,6.729,4,8.924v4.916c-4.777-2.769-8-7.922-8-13.84c0-8.836,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c6.627,0,11.999,5.373,11.999,11.999C71.998,58.863,68.654,63.293,63.999,64.943z"></path>
													</g>
												</g>
											</svg> Cloud Snow Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowSunFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowSunFill">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snow">
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-left" cx="42.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-middle" cx="50.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-right" cx="57.999" cy="59.641" r="2"></circle>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Snow Sun Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snow">
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-left" cx="42.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-middle" cx="50.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-right" cx="57.999" cy="59.641" r="2"></circle>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M63.999,64.943v-4.381c2.39-1.386,3.999-3.963,3.999-6.922c0-4.417-3.581-7.999-7.999-7.999c-1.601,0-3.083,0.48-4.333,1.291c-1.23-5.317-5.974-9.291-11.665-9.291c-6.627,0-11.998,5.373-11.998,12c0,3.549,1.55,6.729,4,8.924v4.916c-4.777-2.769-8-7.922-8-13.84c0-8.836,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c6.627,0,11.999,5.373,11.999,11.999C71.998,58.863,68.654,63.293,63.999,64.943z"></path>
													</g>
												</g>
											</svg> Cloud Snow Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowMoonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowMoonFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snow">
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-left" cx="42.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-middle" cx="50.001" cy="59.641" r="2"></circle>
														<circle class="climacon_component climacon_component-stroke climacon_component-stroke_snow climacon_component-stroke_snow-right" cx="57.999" cy="59.641" r="2"></circle>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Snow Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M50,65.641c-1.104,0-2-0.896-2-2c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2S51.104,65.641,50,65.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snowAlt">
														<g class="climacon_component climacon_component climacon_component-snowAlt" clip-path="url(#snowFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowAlt" d="M43.072,59.641c0.553-0.957,1.775-1.283,2.732-0.731L48,60.176v-2.535c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.535l2.195-1.268c0.957-0.551,2.18-0.225,2.73,0.732c0.553,0.957,0.225,2.18-0.73,2.731l-2.196,1.269l2.196,1.268c0.955,0.553,1.283,1.775,0.73,2.732c-0.552,0.954-1.773,1.282-2.73,0.729L52,67.104v2.535c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2v-2.535l-2.195,1.269c-0.957,0.553-2.18,0.226-2.732-0.729c-0.552-0.957-0.225-2.181,0.732-2.732L46,63.641l-2.195-1.268C42.848,61.82,42.521,60.598,43.072,59.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M61.998,65.461v-4.082c3.447-0.891,6-4.012,6-7.738c0-4.417-3.582-7.999-7.999-7.999c-1.601,0-3.084,0.48-4.334,1.291c-1.231-5.317-5.973-9.291-11.664-9.291c-6.627,0-11.999,5.373-11.999,12c0,4.438,2.417,8.305,5.999,10.379v4.444c-5.86-2.375-9.998-8.112-9.998-14.825c0-8.835,7.162-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.336-0.205,2.033-0.205c6.626,0,11.998,5.373,11.998,11.998C71.997,59.586,67.671,64.506,61.998,65.461z"></path>
													</g>
												</g>
											</svg> Cloud Snow Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowAltFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snowAlt">
														<g class="climacon_component climacon_component climacon_component-snowAlt">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowAlt" d="M43.072,59.641c0.553-0.957,1.775-1.283,2.732-0.731L48,60.176v-2.535c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.535l2.195-1.268c0.957-0.551,2.18-0.225,2.73,0.732c0.553,0.957,0.225,2.18-0.73,2.731l-2.196,1.269l2.196,1.268c0.955,0.553,1.283,1.775,0.73,2.732c-0.552,0.954-1.773,1.282-2.73,0.729L52,67.104v2.535c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2v-2.535l-2.195,1.269c-0.957,0.553-2.18,0.226-2.732-0.729c-0.552-0.957-0.225-2.181,0.732-2.732L46,63.641l-2.195-1.268C42.848,61.82,42.521,60.598,43.072,59.641z"></path>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_snowAlt" fill="#FFFFFF" cx="50" cy="63.641" r="2"></circle>
														</g>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Snow Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowSunAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M50,65.641c-1.104,0-2-0.896-2-2c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2S51.104,65.641,50,65.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowSunAlt">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snowAlt">
														<g class="climacon_component climacon_component climacon_component-snowAlt" clip-path="url(#snowFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowAlt" d="M43.072,59.641c0.553-0.957,1.775-1.283,2.732-0.731L48,60.176v-2.535c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.535l2.195-1.268c0.957-0.551,2.18-0.225,2.73,0.732c0.553,0.957,0.225,2.18-0.73,2.731l-2.196,1.269l2.196,1.268c0.955,0.553,1.283,1.775,0.73,2.732c-0.552,0.954-1.773,1.282-2.73,0.729L52,67.104v2.535c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2v-2.535l-2.195,1.269c-0.957,0.553-2.18,0.226-2.732-0.729c-0.552-0.957-0.225-2.181,0.732-2.732L46,63.641l-2.195-1.268C42.848,61.82,42.521,60.598,43.072,59.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M61.998,65.461v-4.082c3.447-0.891,6-4.012,6-7.738c0-4.417-3.582-7.999-7.999-7.999c-1.601,0-3.084,0.48-4.334,1.291c-1.231-5.317-5.973-9.291-11.664-9.291c-6.627,0-11.999,5.373-11.999,12c0,4.438,2.417,8.305,5.999,10.379v4.444c-5.86-2.375-9.998-8.112-9.998-14.825c0-8.835,7.162-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.336-0.205,2.033-0.205c6.626,0,11.998,5.373,11.998,11.998C71.997,59.586,67.671,64.506,61.998,65.461z"></path>
													</g>
												</g>
											</svg> Cloud Snow Sun Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowSunAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowSunAltFill">
													<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
														<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snowAlt">
														<g class="climacon_component climacon_component climacon_component-snowAlt">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowAlt" d="M43.072,59.641c0.553-0.957,1.775-1.283,2.732-0.731L48,60.176v-2.535c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.535l2.195-1.268c0.957-0.551,2.18-0.225,2.73,0.732c0.553,0.957,0.225,2.18-0.73,2.731l-2.196,1.269l2.196,1.268c0.955,0.553,1.283,1.775,0.73,2.732c-0.552,0.954-1.773,1.282-2.73,0.729L52,67.104v2.535c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2v-2.535l-2.195,1.269c-0.957,0.553-2.18,0.226-2.732-0.729c-0.552-0.957-0.225-2.181,0.732-2.732L46,63.641l-2.195-1.268C42.848,61.82,42.521,60.598,43.072,59.641z"></path>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_snowAlt" fill="#FFFFFF" cx="50" cy="63.641" r="2"></circle>
														</g>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Snow Sun Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M50,65.641c-1.104,0-2-0.896-2-2c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2S51.104,65.641,50,65.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowAlt">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snowAlt">
														<g class="climacon_component climacon_component climacon_component-snowAlt" clip-path="url(#snowFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowAlt" d="M43.072,59.641c0.553-0.957,1.775-1.283,2.732-0.731L48,60.176v-2.535c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.535l2.195-1.268c0.957-0.551,2.18-0.225,2.73,0.732c0.553,0.957,0.225,2.18-0.73,2.731l-2.196,1.269l2.196,1.268c0.955,0.553,1.283,1.775,0.73,2.732c-0.552,0.954-1.773,1.282-2.73,0.729L52,67.104v2.535c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2v-2.535l-2.195,1.269c-0.957,0.553-2.18,0.226-2.732-0.729c-0.552-0.957-0.225-2.181,0.732-2.732L46,63.641l-2.195-1.268C42.848,61.82,42.521,60.598,43.072,59.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M61.998,65.461v-4.082c3.447-0.891,6-4.012,6-7.738c0-4.417-3.582-7.999-7.999-7.999c-1.601,0-3.084,0.48-4.334,1.291c-1.231-5.317-5.973-9.291-11.664-9.291c-6.627,0-11.999,5.373-11.999,12c0,4.438,2.417,8.305,5.999,10.379v4.444c-5.86-2.375-9.998-8.112-9.998-14.825c0-8.835,7.162-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.336-0.205,2.033-0.205c6.626,0,11.998,5.373,11.998,11.998C71.997,59.586,67.671,64.506,61.998,65.461z"></path>
													</g>
												</g>
											</svg> Cloud Snow Moon Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudSnowAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudSnowAltFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-snowAlt">
														<g class="climacon_component climacon_component climacon_component-snowAlt">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_snowAlt" d="M43.072,59.641c0.553-0.957,1.775-1.283,2.732-0.731L48,60.176v-2.535c0-1.104,0.896-2,2-2c1.104,0,2,0.896,2,2v2.535l2.195-1.268c0.957-0.551,2.18-0.225,2.73,0.732c0.553,0.957,0.225,2.18-0.73,2.731l-2.196,1.269l2.196,1.268c0.955,0.553,1.283,1.775,0.73,2.732c-0.552,0.954-1.773,1.282-2.73,0.729L52,67.104v2.535c0,1.105-0.896,2-2,2c-1.104,0-2-0.895-2-2v-2.535l-2.195,1.269c-0.957,0.553-2.18,0.226-2.732-0.729c-0.552-0.957-0.225-2.181,0.732-2.732L46,63.641l-2.195-1.268C42.848,61.82,42.521,60.598,43.072,59.641z"></path>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_snowAlt" fill="#FFFFFF" cx="50" cy="63.641" r="2"></circle>
														</g>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Snow Moon Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFog" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudFog">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-top" d="M69.998,57.641H30.003c-1.104,0-2-0.895-2-2c0-1.104,0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,56.746,71.104,57.641,69.998,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-middle" d="M69.998,65.641H30.003c-1.104,0-2-0.896-2-2s0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,64.744,71.104,65.641,69.998,65.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-bottom" d="M30.003,69.639h39.995c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H30.003c-1.104,0-2-0.895-2-2C28.003,70.535,28.898,69.639,30.003,69.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,45.643c-1.601,0-3.083,0.48-4.333,1.291c-1.232-5.317-5.974-9.291-11.665-9.291c-6.626,0-11.998,5.373-11.998,12h-4c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c5.222,0,9.651,3.342,11.301,8h-4.381C65.535,47.253,62.958,45.643,59.999,45.643z"></path>
													</g>
												</g>
											</svg> Cloud Fog
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudFog">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-top" d="M69.998,57.641H30.003c-1.104,0-2-0.895-2-2c0-1.104,0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,56.746,71.104,57.641,69.998,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-middle" d="M69.998,65.641H30.003c-1.104,0-2-0.896-2-2s0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,64.744,71.104,65.641,69.998,65.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-bottom" d="M30.003,69.639h39.995c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H30.003c-1.104,0-2-0.895-2-2C28.003,70.535,28.898,69.639,30.003,69.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,45.643c-1.601,0-3.083,0.48-4.333,1.291c-1.232-5.317-5.974-9.291-11.665-9.291c-6.626,0-11.998,5.373-11.998,12h-4c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c5.222,0,9.651,3.342,11.301,8h-4.381C65.535,47.253,62.958,45.643,59.999,45.643z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M71.287,49.355c-1.659-4.632-6.08-7.954-11.289-7.954c-0.695,0-1.369,0.091-2.033,0.205C55.229,36.72,50.004,33.413,44,33.413c-8.824,0-15.977,7.134-15.996,15.942H71.287z"></path>
														<path fill="#FFFFFF" class="climacon_component climacon_component-fill climacon_component-fill_cloud" d="M66.897,49.376c-1.389-2.369-3.955-3.965-6.899-3.965c-1.602,0-3.082,0.48-4.334,1.291c-1.23-5.316-5.973-9.291-11.664-9.291c-6.615,0-11.977,5.354-11.996,11.965H66.897z"></path>
													</g>
												</g>
											</svg> Cloud Fog Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogSun">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-top" d="M69.998,57.641H30.003c-1.104,0-2-0.895-2-2c0-1.104,0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,56.746,71.104,57.641,69.998,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-middle" d="M69.998,65.641H30.003c-1.104,0-2-0.896-2-2s0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,64.744,71.104,65.641,69.998,65.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-bottom" d="M30.003,69.639h39.995c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H30.003c-1.104,0-2-0.895-2-2C28.003,70.535,28.898,69.639,30.003,69.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,45.643c-1.601,0-3.083,0.48-4.333,1.291c-1.232-5.317-5.974-9.291-11.665-9.291c-6.626,0-11.998,5.373-11.998,12h-4c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c5.222,0,9.651,3.342,11.301,8h-4.381C65.535,47.253,62.958,45.643,59.999,45.643z"></path>
													</g>
												</g>
											</svg> Cloud Fog Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogSunFill" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogSunFill">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
																<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-top" d="M69.998,57.641H30.003c-1.104,0-2-0.895-2-2c0-1.104,0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,56.746,71.104,57.641,69.998,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-middle" d="M69.998,65.641H30.003c-1.104,0-2-0.896-2-2s0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,64.744,71.104,65.641,69.998,65.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-bottom" d="M30.003,69.639h39.995c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H30.003c-1.104,0-2-0.895-2-2C28.003,70.535,28.898,69.639,30.003,69.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,45.643c-1.601,0-3.083,0.48-4.333,1.291c-1.232-5.317-5.974-9.291-11.665-9.291c-6.626,0-11.998,5.373-11.998,12h-4c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c5.222,0,9.651,3.342,11.301,8h-4.381C65.535,47.253,62.958,45.643,59.999,45.643z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M71.287,49.355c-1.659-4.632-6.08-7.954-11.289-7.954c-0.695,0-1.369,0.091-2.033,0.205C55.229,36.72,50.004,33.413,44,33.413c-8.824,0-15.977,7.134-15.996,15.942H71.287z"></path>
														<path fill="#FFFFFF" class="climacon_component climacon_component-fill climacon_component-fill_cloud" d="M66.897,49.376c-1.389-2.369-3.955-3.965-6.899-3.965c-1.602,0-3.082,0.48-4.334,1.291c-1.23-5.316-5.973-9.291-11.664-9.291c-6.615,0-11.977,5.354-11.996,11.965H66.897z"></path>
													</g>
												</g>
											</svg> Cloud Fog Sun Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-top" d="M69.998,57.641H30.003c-1.104,0-2-0.895-2-2c0-1.104,0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,56.746,71.104,57.641,69.998,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-middle" d="M69.998,65.641H30.003c-1.104,0-2-0.896-2-2s0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,64.744,71.104,65.641,69.998,65.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-bottom" d="M30.003,69.639h39.995c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H30.003c-1.104,0-2-0.895-2-2C28.003,70.535,28.898,69.639,30.003,69.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,45.643c-1.601,0-3.083,0.48-4.333,1.291c-1.232-5.317-5.974-9.291-11.665-9.291c-6.626,0-11.998,5.373-11.998,12h-4c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c5.222,0,9.651,3.342,11.301,8h-4.381C65.535,47.253,62.958,45.643,59.999,45.643z"></path>
													</g>
												</g>
											</svg> Cloud Fog Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogMoonFill" xmlns="http://www.w3.org/2000/svg" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogMoonFill">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
															<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-top" d="M69.998,57.641H30.003c-1.104,0-2-0.895-2-2c0-1.104,0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,56.746,71.104,57.641,69.998,57.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-middle" d="M69.998,65.641H30.003c-1.104,0-2-0.896-2-2s0.896-2,2-2h39.995c1.104,0,2,0.896,2,2C71.998,64.744,71.104,65.641,69.998,65.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine climacon_component-stroke_fogLine-bottom" d="M30.003,69.639h39.995c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H30.003c-1.104,0-2-0.895-2-2C28.003,70.535,28.898,69.639,30.003,69.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,45.643c-1.601,0-3.083,0.48-4.333,1.291c-1.232-5.317-5.974-9.291-11.665-9.291c-6.626,0-11.998,5.373-11.998,12h-4c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.113,1.337-0.205,2.033-0.205c5.222,0,9.651,3.342,11.301,8h-4.381C65.535,47.253,62.958,45.643,59.999,45.643z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M71.287,49.355c-1.659-4.632-6.08-7.954-11.289-7.954c-0.695,0-1.369,0.091-2.033,0.205C55.229,36.72,50.004,33.413,44,33.413c-8.824,0-15.977,7.134-15.996,15.942H71.287z"></path>
														<path fill="#FFFFFF" class="climacon_component climacon_component-fill climacon_component-fill_cloud" d="M66.897,49.376c-1.389-2.369-3.955-3.965-6.899-3.965c-1.602,0-3.082,0.48-4.334,1.291c-1.23-5.316-5.973-9.291-11.664-9.291c-6.615,0-11.977,5.354-11.996,11.965H66.897z"></path>
													</g>
												</g>
											</svg> Cloud Fog Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogAlt" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogAlt">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,55.641c-0.262-0.646-0.473-1.314-0.648-2h43.47c0,0.685-0.069,1.349-0.181,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M36.263,35.643c2.294-1.271,4.93-1.999,7.738-1.999c2.806,0,5.436,0.73,7.728,1.999H36.263z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M28.142,47.642c0.085-0.682,0.218-1.347,0.387-1.999h40.396c0.552,0.613,1.039,1.281,1.455,1.999H28.142z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,43.643c0.281-0.693,0.613-1.359,0.984-2h27.682c0.04,0.068,0.084,0.135,0.123,0.205c0.664-0.114,1.339-0.205,2.033-0.205c2.451,0,4.729,0.738,6.627,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M31.524,39.643c0.58-0.723,1.225-1.388,1.92-2h21.123c0.689,0.61,1.326,1.28,1.902,2H31.524z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.816,51.641H28.142c-0.082-0.656-0.139-1.32-0.139-1.999h43.298C71.527,50.285,71.702,50.953,71.816,51.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.301,57.641c-0.246,0.699-0.555,1.367-0.921,2H31.524c-0.505-0.629-0.957-1.299-1.363-2H71.301z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M33.444,61.641h35.48c-0.68,0.758-1.447,1.435-2.299,2H36.263C35.247,63.078,34.309,62.4,33.444,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Fog Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogAltFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogAltFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<g class="climacon_componentWrap climacon_componentWrap_cloud">
															<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														</g>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,55.641c-0.262-0.646-0.473-1.314-0.648-2h43.47c0,0.685-0.069,1.349-0.181,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M36.263,35.643c2.294-1.271,4.93-1.999,7.738-1.999c2.806,0,5.436,0.73,7.728,1.999H36.263z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M28.142,47.642c0.085-0.682,0.218-1.347,0.387-1.999h40.396c0.552,0.613,1.039,1.281,1.455,1.999H28.142z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,43.643c0.281-0.693,0.613-1.359,0.984-2h27.682c0.04,0.068,0.084,0.135,0.123,0.205c0.664-0.114,1.339-0.205,2.033-0.205c2.451,0,4.729,0.738,6.627,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M31.524,39.643c0.58-0.723,1.225-1.388,1.92-2h21.123c0.689,0.61,1.326,1.28,1.902,2H31.524z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.816,51.641H28.142c-0.082-0.656-0.139-1.32-0.139-1.999h43.298C71.527,50.285,71.702,50.953,71.816,51.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.301,57.641c-0.246,0.699-0.555,1.367-0.921,2H31.524c-0.505-0.629-0.957-1.299-1.363-2H71.301z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M33.444,61.641h35.48c-0.68,0.758-1.447,1.435-2.299,2H36.263C35.247,63.078,34.309,62.4,33.444,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Fog Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogSunAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogSunAlt">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,55.641c-0.262-0.646-0.473-1.314-0.648-2h43.47c0,0.685-0.069,1.349-0.181,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M36.263,35.643c2.294-1.271,4.93-1.999,7.738-1.999c2.806,0,5.436,0.73,7.728,1.999H36.263z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M28.142,47.642c0.085-0.682,0.218-1.347,0.387-1.999h40.396c0.552,0.613,1.039,1.281,1.455,1.999H28.142z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,43.643c0.281-0.693,0.613-1.359,0.984-2h27.682c0.04,0.068,0.084,0.135,0.123,0.205c0.664-0.114,1.339-0.205,2.033-0.205c2.451,0,4.729,0.738,6.627,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M31.524,39.643c0.58-0.723,1.225-1.388,1.92-2h21.123c0.689,0.61,1.326,1.28,1.902,2H31.524z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.816,51.641H28.142c-0.082-0.656-0.139-1.32-0.139-1.999h43.298C71.527,50.285,71.702,50.953,71.816,51.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.301,57.641c-0.246,0.699-0.555,1.367-0.921,2H31.524c-0.505-0.629-0.957-1.299-1.363-2H71.301z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M33.444,61.641h35.48c-0.68,0.758-1.447,1.435-2.299,2H36.263C35.247,63.078,34.309,62.4,33.444,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Fog Sun Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogSunAltFill" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogSunAlt">
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
													</g>
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
																<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,55.641c-0.262-0.646-0.473-1.314-0.648-2h43.47c0,0.685-0.069,1.349-0.181,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M36.263,35.643c2.294-1.271,4.93-1.999,7.738-1.999c2.806,0,5.436,0.73,7.728,1.999H36.263z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M28.142,47.642c0.085-0.682,0.218-1.347,0.387-1.999h40.396c0.552,0.613,1.039,1.281,1.455,1.999H28.142z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,43.643c0.281-0.693,0.613-1.359,0.984-2h27.682c0.04,0.068,0.084,0.135,0.123,0.205c0.664-0.114,1.339-0.205,2.033-0.205c2.451,0,4.729,0.738,6.627,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M31.524,39.643c0.58-0.723,1.225-1.388,1.92-2h21.123c0.689,0.61,1.326,1.28,1.902,2H31.524z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.816,51.641H28.142c-0.082-0.656-0.139-1.32-0.139-1.999h43.298C71.527,50.285,71.702,50.953,71.816,51.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.301,57.641c-0.246,0.699-0.555,1.367-0.921,2H31.524c-0.505-0.629-0.957-1.299-1.363-2H71.301z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M33.444,61.641h35.48c-0.68,0.758-1.447,1.435-2.299,2H36.263C35.247,63.078,34.309,62.4,33.444,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Fog Sun Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogMoonAlt" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,65.638c-2.775,0-12.801,0-15.998,0c-8.836,0-15.998-7.162-15.998-15.998c0-8.835,7.162-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12C71.941,60.265,66.57,65.638,59.943,65.638z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogMoon">
													<g clip-path="url(#newMoonCloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,55.641c-0.262-0.646-0.473-1.314-0.648-2h43.47c0,0.685-0.069,1.349-0.181,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M36.263,35.643c2.294-1.271,4.93-1.999,7.738-1.999c2.806,0,5.436,0.73,7.728,1.999H36.263z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M28.142,47.642c0.085-0.682,0.218-1.347,0.387-1.999h40.396c0.552,0.613,1.039,1.281,1.455,1.999H28.142z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,43.643c0.281-0.693,0.613-1.359,0.984-2h27.682c0.04,0.068,0.084,0.135,0.123,0.205c0.664-0.114,1.339-0.205,2.033-0.205c2.451,0,4.729,0.738,6.627,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M31.524,39.643c0.58-0.723,1.225-1.388,1.92-2h21.123c0.689,0.61,1.326,1.28,1.902,2H31.524z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.816,51.641H28.142c-0.082-0.656-0.139-1.32-0.139-1.999h43.298C71.527,50.285,71.702,50.953,71.816,51.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.301,57.641c-0.246,0.699-0.555,1.367-0.921,2H31.524c-0.505-0.629-0.957-1.299-1.363-2H71.301z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M33.444,61.641h35.48c-0.68,0.758-1.447,1.435-2.299,2H36.263C35.247,63.078,34.309,62.4,33.444,61.641z"></path>
													</g>
												</g>
											</svg> Cloud Fog Moon Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudFogMoonAltFill" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,65.638c-2.775,0-12.801,0-15.998,0c-8.836,0-15.998-7.162-15.998-15.998c0-8.835,7.162-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12C71.941,60.265,66.57,65.638,59.943,65.638z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudFogMoonAltFill">
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-Fog">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,55.641c-0.262-0.646-0.473-1.314-0.648-2h43.47c0,0.685-0.069,1.349-0.181,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M36.263,35.643c2.294-1.271,4.93-1.999,7.738-1.999c2.806,0,5.436,0.73,7.728,1.999H36.263z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M28.142,47.642c0.085-0.682,0.218-1.347,0.387-1.999h40.396c0.552,0.613,1.039,1.281,1.455,1.999H28.142z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M29.177,43.643c0.281-0.693,0.613-1.359,0.984-2h27.682c0.04,0.068,0.084,0.135,0.123,0.205c0.664-0.114,1.339-0.205,2.033-0.205c2.451,0,4.729,0.738,6.627,2H29.177z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M31.524,39.643c0.58-0.723,1.225-1.388,1.92-2h21.123c0.689,0.61,1.326,1.28,1.902,2H31.524z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.816,51.641H28.142c-0.082-0.656-0.139-1.32-0.139-1.999h43.298C71.527,50.285,71.702,50.953,71.816,51.641z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M71.301,57.641c-0.246,0.699-0.555,1.367-0.921,2H31.524c-0.505-0.629-0.957-1.299-1.363-2H71.301z"></path>
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_fogLine" d="M33.444,61.641h35.48c-0.68,0.758-1.447,1.435-2.299,2H36.263C35.247,63.078,34.309,62.4,33.444,61.641z"></path>
													</g>
													<g clip-path="url(#newMoonCloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_moonBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
															<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
														</g>
													</g>
												</g>
											</svg> Cloud Fog Moon Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudLightning" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudLightning">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-lightning">
														<polygon class="climacon_component climacon_component-stroke climacon_component-stroke_lightning" points="48.001,51.641 57.999,51.641 52,61.641 58.999,61.641 46.001,77.639 49.601,65.641 43.001,65.641 "></polygon>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,65.641c-0.28,0-0.649,0-1.062,0l3.584-4.412c3.182-1.057,5.478-4.053,5.478-7.588c0-4.417-3.581-7.998-7.999-7.998c-1.602,0-3.083,0.48-4.333,1.29c-1.231-5.316-5.974-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,12c0,5.446,3.632,10.039,8.604,11.503l-1.349,3.777c-6.52-2.021-11.255-8.098-11.255-15.282c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.114,1.338-0.205,2.033-0.205c6.627,0,11.999,5.371,11.999,11.999C71.999,60.268,66.626,65.641,59.999,65.641z"></path>
													</g>
												</g>
											</svg> Cloud Lightning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudLightningFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudLightningFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-lightning">
														<polygon class="climacon_component climacon_component-stroke climacon_component-stroke_lightning" points="48.001,51.641 57.999,51.641 52,61.641 58.999,61.641 46.001,77.639 49.601,65.641 43.001,65.641 "></polygon>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Lightning Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudLightningSun" viewbox="15 15 70 70">
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M57.945,49.641c-4.417,0-8-3.582-8-7.999c0-4.418,3.582-7.999,8-7.999s7.998,3.581,7.998,7.999C65.943,46.059,62.362,49.641,57.945,49.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h20.947V63.481c-4.778-2.767-8-7.922-8-13.84c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,5.262-3.394,9.723-8.107,11.341V85H85V15H15z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudLightning">
													<g clip-path="url(#cloudSunFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody" clip-path="url(#sunCloudFillClip)">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-lightning">
														<polygon class="climacon_component climacon_component-stroke climacon_component-stroke_lightning" points="48.001,51.641 57.999,51.641 52,61.641 58.999,61.641 46.001,77.639 49.601,65.641 43.001,65.641 "></polygon>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,65.641c-0.28,0-0.649,0-1.062,0l3.584-4.412c3.182-1.057,5.478-4.053,5.478-7.588c0-4.417-3.581-7.998-7.999-7.998c-1.602,0-3.083,0.48-4.333,1.29c-1.231-5.316-5.974-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,12c0,5.446,3.632,10.039,8.604,11.503l-1.349,3.777c-6.52-2.021-11.255-8.098-11.255-15.282c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.114,1.338-0.205,2.033-0.205c6.627,0,11.999,5.371,11.999,11.999C71.999,60.268,66.626,65.641,59.999,65.641z"></path>
													</g>
												</g>
											</svg> Cloud Lightning Sun
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudLightningSunFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudLightning">
													<g clip-path="url(#clip)">
														<g class="climacon_componentWrap climacon_componentWrap-sun climacon_componentWrap-sun_cloud">
															<g class="climacon_componentWrap climacon_componentWrap_sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M80.029,43.611h-3.998c-1.105,0-2-0.896-2-1.999s0.895-2,2-2h3.998c1.104,0,2,0.896,2,2S81.135,43.611,80.029,43.611z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,30.3c-0.781,0.781-2.049,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.779-0.781,2.047-0.781,2.828,0c0.779,0.781,0.779,2.047,0,2.828L72.174,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,25.614c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C60.033,24.718,59.135,25.614,58.033,25.614z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,30.3l-2.827-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.781,2.047-0.781,2.827,0l2.827,2.828c0.781,0.781,0.781,2.047,0,2.828C45.939,31.081,44.673,31.081,43.892,30.3z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M42.033,41.612c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.998-0.896-1.998-1.999s0.896-2,1.998-2h4C41.139,39.612,42.033,40.509,42.033,41.612z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M43.892,52.925c0.781-0.78,2.048-0.78,2.827,0c0.781,0.78,0.781,2.047,0,2.828l-2.827,2.827c-0.78,0.781-2.047,0.781-2.827,0c-0.781-0.78-0.781-2.047,0-2.827L43.892,52.925z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M58.033,57.61c1.104,0,2,0.895,2,1.999v4c0,1.104-0.896,2-2,2c-1.105,0-2-0.896-2-2v-4C56.033,58.505,56.928,57.61,58.033,57.61z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M72.174,52.925l2.828,2.828c0.779,0.78,0.779,2.047,0,2.827c-0.781,0.781-2.049,0.781-2.828,0l-2.828-2.827c-0.781-0.781-0.781-2.048,0-2.828C70.125,52.144,71.391,52.144,72.174,52.925z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="58.033" cy="41.612" r="11.999"></circle>
																<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="58.033" cy="41.612" r="7.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-lightning">
														<polygon class="climacon_component climacon_component-stroke climacon_component-stroke_lightning" points="48.001,51.641 57.999,51.641 52,61.641 58.999,61.641 46.001,77.639 49.601,65.641 43.001,65.641 "></polygon>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Lightning Sun Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudLightningMoon" viewbox="15 15 70 70">
												<clippath>
													<path d="M0,0v100h100V0H0z M60.943,46.641c-4.418,0-7.999-3.582-7.999-7.999c0-3.803,2.655-6.979,6.211-7.792c0.903,4.854,4.726,8.676,9.579,9.58C67.922,43.986,64.745,46.641,60.943,46.641z"></path>
												</clippath>
												<clippath>
													<path d="M15,15v70h70V15H15z M59.943,61.639c-3.02,0-12.381,0-15.999,0c-6.626,0-11.998-5.371-11.998-11.998c0-6.627,5.372-11.999,11.998-11.999c5.691,0,10.434,3.974,11.665,9.29c1.252-0.81,2.733-1.291,4.334-1.291c4.418,0,8,3.582,8,8C67.943,58.057,64.361,61.639,59.943,61.639z"></path>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-cloudLightningMoon">
													<g clip-path="url(#cloudFillClip)">
														<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud" clip-path="url(#moonCloudFillClip)">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-lightning">
														<polygon class="climacon_component climacon_component-stroke climacon_component-stroke_lightning" points="48.001,51.641 57.999,51.641 52,61.641 58.999,61.641 46.001,77.639 49.601,65.641 43.001,65.641 "></polygon>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M59.999,65.641c-0.28,0-0.649,0-1.062,0l3.584-4.412c3.182-1.057,5.478-4.053,5.478-7.588c0-4.417-3.581-7.998-7.999-7.998c-1.602,0-3.083,0.48-4.333,1.29c-1.231-5.316-5.974-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,12c0,5.446,3.632,10.039,8.604,11.503l-1.349,3.777c-6.52-2.021-11.255-8.098-11.255-15.282c0-8.835,7.163-15.999,15.998-15.999c6.004,0,11.229,3.312,13.965,8.204c0.664-0.114,1.338-0.205,2.033-0.205c6.627,0,11.999,5.371,11.999,11.999C71.999,60.268,66.626,65.641,59.999,65.641z"></path>
													</g>
												</g>
											</svg> Cloud Lightning Moon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_cloudLightningMoonFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-cloudLightningMoonFill">
													<g class="climacon_wrapperComponent climacon_wrapperComponent-moon climacon_componentWrap-moon_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.023,50.641c-6.627,0-11.999-5.372-11.999-11.998c0-6.627,5.372-11.999,11.999-11.999c0.755,0,1.491,0.078,2.207,0.212c-0.132,0.576-0.208,1.173-0.208,1.788c0,4.418,3.582,7.999,8,7.999c0.614,0,1.212-0.076,1.788-0.208c0.133,0.717,0.211,1.452,0.211,2.208C73.021,45.269,67.649,50.641,61.023,50.641z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_moon" fill="#FFFFFF" d="M59.235,30.851c-3.556,0.813-6.211,3.989-6.211,7.792c0,4.417,3.581,7.999,7.999,7.999c3.802,0,6.979-2.655,7.791-6.211C63.961,39.527,60.139,35.705,59.235,30.851z"></path>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-lightning">
														<polygon class="climacon_component climacon_component-stroke climacon_component-stroke_lightning" points="48.001,51.641 57.999,51.641 52,61.641 58.999,61.641 46.001,77.639 49.601,65.641 43.001,65.641 "></polygon>
													</g>
													<g class="climacon_componentWrap climacon_componentWrap_cloud">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_cloud" d="M43.945,65.639c-8.835,0-15.998-7.162-15.998-15.998c0-8.836,7.163-15.998,15.998-15.998c6.004,0,11.229,3.312,13.965,8.203c0.664-0.113,1.338-0.205,2.033-0.205c6.627,0,11.998,5.373,11.998,12c0,6.625-5.371,11.998-11.998,11.998C57.168,65.639,47.143,65.639,43.945,65.639z"></path>
														<path class="climacon_component climacon_component-fill climacon_component-fill_cloud" fill="#FFFFFF" d="M59.943,61.639c4.418,0,8-3.582,8-7.998c0-4.417-3.582-8-8-8c-1.601,0-3.082,0.481-4.334,1.291c-1.23-5.316-5.973-9.29-11.665-9.29c-6.626,0-11.998,5.372-11.998,11.999c0,6.626,5.372,11.998,11.998,11.998C47.562,61.639,56.924,61.639,59.943,61.639z"></path>
													</g>
												</g>
											</svg> Cloud Lightning Moon Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunrise" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-sunrise">
													<g class="climacon_componentWrap climacon_componentWrap-sunrise">
														<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M32.003,54h-4c-1.104,0-2,0.896-2,2s0.896,2,2,2h4c1.104,0,2-0.896,2-2S33.106,54,32.003,54z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northEast" d="M38.688,41.859l-2.828-2.828c-0.781-0.78-2.047-0.78-2.828,0c-0.781,0.781-0.781,2.047,0,2.828l2.828,2.828c0.781,0.781,2.047,0.781,2.828,0C39.469,43.906,39.469,42.641,38.688,41.859z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M50.001,40.002c1.104,0,1.999-0.896,1.999-2v-3.999c0-1.104-0.896-2-1.999-2c-1.105,0-2,0.896-2,2v3.999C48.001,39.106,48.896,40.002,50.001,40.002z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northWest" d="M66.969,39.031c-0.779-0.78-2.048-0.78-2.828,0l-2.828,2.828c-0.779,0.781-0.779,2.047,0,2.828c0.781,0.781,2.049,0.781,2.828,0l2.828-2.828C67.749,41.078,67.749,39.812,66.969,39.031z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-west" d="M71.997,54h-3.999c-1.104,0-1.999,0.896-1.999,2s0.896,2,1.999,2h3.999c1.104,0,2-0.896,2-2S73.104,54,71.997,54z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M50.001,44.002c-6.627,0-11.999,5.371-11.999,11.998c0,1.404,0.254,2.747,0.697,3.999h4.381c-0.683-1.177-1.079-2.54-1.079-3.999c0-4.418,3.582-7.999,8-7.999c4.417,0,7.998,3.581,7.998,7.999c0,1.459-0.396,2.822-1.078,3.999h4.381c0.443-1.252,0.697-2.595,0.697-3.999C61.999,49.373,56.627,44.002,50.001,44.002z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-arrow">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_arrow climacon_component-stroke_arrow-up" d="M50.001,63.046c0.552,0,0.999-0.447,0.999-1v-3.827l2.536,2.535c0.39,0.391,1.022,0.391,1.414,0c0.39-0.391,0.39-1.023,0-1.414l-4.242-4.242c-0.391-0.391-1.024-0.391-1.414,0l-4.242,4.242c-0.391,0.391-0.391,1.023,0,1.414c0.391,0.391,1.023,0.391,1.414,0l2.535-2.535v3.827C49.001,62.599,49.448,63.046,50.001,63.046z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M59.999,63.999H40.001c-1.104,0-1.999,0.896-1.999,2s0.896,1.999,1.999,1.999h19.998c1.104,0,2-0.895,2-1.999S61.104,63.999,59.999,63.999z"></path>
														</g>
													</g>
												</g>
											</svg> Sunrise
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunriseFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-sunriseFill">
													<g class="climacon_componentWrap climacon_componentWrap-sunrise">
														<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M71.997,57.999h-3.998c-1.104,0-2-0.896-2-1.999s0.896-2,2-2h3.998c1.104,0,2,0.896,2,2S73.104,57.999,71.997,57.999z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M64.143,44.688c-0.781,0.781-2.05,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.778-0.781,2.047-0.781,2.828,0c0.778,0.781,0.778,2.047,0,2.828L64.143,44.688z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M50.001,40.002c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C52.001,39.106,51.104,40.002,50.001,40.002z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M35.86,44.688l-2.828-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.781-0.781,2.047-0.781,2.828,0l2.828,2.828c0.781,0.781,0.781,2.047,0,2.828C37.907,45.469,36.641,45.469,35.86,44.688z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M34.002,56c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.999-0.896-1.999-1.999s0.896-2,1.999-2h4C33.107,54,34.002,54.896,34.002,56z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="50.001" cy="56" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="50.001" cy="56" r="7.999"></circle>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-arrow">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_arrow climacon_component-stroke_arrow-up" d="M50.001,63.046c0.552,0,0.999-0.447,0.999-1v-3.827l2.536,2.535c0.39,0.391,1.022,0.391,1.414,0c0.39-0.391,0.39-1.023,0-1.414l-4.242-4.242c-0.391-0.391-1.024-0.391-1.414,0l-4.242,4.242c-0.391,0.391-0.391,1.023,0,1.414c0.391,0.391,1.023,0.391,1.414,0l2.535-2.535v3.827C49.001,62.599,49.448,63.046,50.001,63.046z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M59.999,63.999H40.001c-1.104,0-1.999,0.896-1.999,2s0.896,1.999,1.999,1.999h19.998c1.104,0,2-0.895,2-1.999S61.104,63.999,59.999,63.999z"></path>
														</g>
													</g>
												</g>
											</svg> Sunrise Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunset" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-sunset">
													<g class="climacon_componentWrap climacon_componentWrap-sunset">
														<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M32.003,54h-4c-1.104,0-2,0.896-2,2s0.896,2,2,2h4c1.104,0,2-0.896,2-2S33.106,54,32.003,54z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northEast" d="M38.688,41.859l-2.828-2.828c-0.781-0.78-2.047-0.78-2.828,0c-0.781,0.781-0.781,2.047,0,2.828l2.828,2.828c0.781,0.781,2.047,0.781,2.828,0C39.469,43.906,39.469,42.641,38.688,41.859z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M50.001,40.002c1.104,0,1.999-0.896,1.999-2v-3.999c0-1.104-0.896-2-1.999-2c-1.105,0-2,0.896-2,2v3.999C48.001,39.106,48.896,40.002,50.001,40.002z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northWest" d="M66.969,39.031c-0.779-0.78-2.048-0.78-2.828,0l-2.828,2.828c-0.779,0.781-0.779,2.047,0,2.828c0.781,0.781,2.049,0.781,2.828,0l2.828-2.828C67.749,41.078,67.749,39.812,66.969,39.031z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-west" d="M71.997,54h-3.999c-1.104,0-1.999,0.896-1.999,2s0.896,2,1.999,2h3.999c1.104,0,2-0.896,2-2S73.104,54,71.997,54z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M50.001,44.002c-6.627,0-11.999,5.371-11.999,11.998c0,1.404,0.254,2.747,0.697,3.999h4.381c-0.683-1.177-1.079-2.54-1.079-3.999c0-4.418,3.582-7.999,8-7.999c4.417,0,7.998,3.581,7.998,7.999c0,1.459-0.396,2.822-1.078,3.999h4.381c0.443-1.252,0.697-2.595,0.697-3.999C61.999,49.373,56.627,44.002,50.001,44.002z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-arrow">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_arrow climacon_component-stroke_arrow-down" d="M50,49.107c0.552,0,1,0.448,1,1.002v3.83l2.535-2.535c0.391-0.391,1.022-0.391,1.414,0c0.391,0.391,0.391,1.023,0,1.414l-4.242,4.242c-0.392,0.391-1.022,0.391-1.414,0l-4.242-4.242c-0.391-0.391-0.391-1.023,0-1.414c0.392-0.391,1.023-0.391,1.414,0L49,53.939v-3.83C49,49.555,49.447,49.107,50,49.107z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M59.999,63.999H40.001c-1.104,0-1.999,0.896-1.999,2s0.896,1.999,1.999,1.999h19.998c1.104,0,2-0.895,2-1.999S61.104,63.999,59.999,63.999z"></path>
														</g>
													</g>
												</g>
											</svg> Sunset
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunsetFill" viewbox="15 15 70 70">
												<g class="climacon_iconWrap climacon_iconWrap-sunsetFill">
													<g class="climacon_componentWrap climacon_componentWrap-sunsetFill">
														<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-west" d="M71.997,57.999h-3.998c-1.104,0-2-0.896-2-1.999s0.896-2,2-2h3.998c1.104,0,2,0.896,2,2S73.104,57.999,71.997,57.999z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northWest" d="M64.143,44.688c-0.781,0.781-2.05,0.781-2.828,0c-0.781-0.781-0.781-2.047,0-2.828l2.828-2.828c0.778-0.781,2.047-0.781,2.828,0c0.778,0.781,0.778,2.047,0,2.828L64.143,44.688z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M50.001,40.002c-1.105,0-2-0.896-2-2v-3.999c0-1.104,0.895-2,2-2c1.104,0,2,0.896,2,2v3.999C52.001,39.106,51.104,40.002,50.001,40.002z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northEast" d="M35.86,44.688l-2.828-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.781-0.781,2.047-0.781,2.828,0l2.828,2.828c0.781,0.781,0.781,2.047,0,2.828C37.907,45.469,36.641,45.469,35.86,44.688z"></path>
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M34.002,56c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.999-0.896-1.999-1.999s0.896-2,1.999-2h4C33.107,54,34.002,54.896,34.002,56z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
															<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="50.001" cy="56" r="11.999"></circle>
															<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="50.001" cy="56" r="7.999"></circle>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-arrow">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_arrow climacon_component-stroke_arrow-down" d="M50,49.107c0.552,0,1,0.448,1,1.002v3.83l2.535-2.535c0.391-0.391,1.022-0.391,1.414,0c0.391,0.391,0.391,1.023,0,1.414l-4.242,4.242c-0.392,0.391-1.022,0.391-1.414,0l-4.242-4.242c-0.391-0.391-0.391-1.023,0-1.414c0.392-0.391,1.023-0.391,1.414,0L49,53.939v-3.83C49,49.555,49.447,49.107,50,49.107z"></path>
														</g>
														<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
															<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M59.999,63.999H40.001c-1.104,0-1.999,0.896-1.999,2s0.896,1.999,1.999,1.999h19.998c1.104,0,2-0.895,2-1.999S61.104,63.999,59.999,63.999z"></path>
														</g>
													</g>
												</g>
											</svg> Sunset Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunriseAlt" viewbox="15 15 70 70">
												<clippath>
													<rect x="15" y="15" width="70" height="45"></rect>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-sunriseAlt">
													<g clip-path="url(#sunriseClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sunriseAlt">
															<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M71.997,74.002h-3.999c-1.104,0-2-0.896-2-2c0-1.105,0.896-2,2-2h3.999c1.104,0,2,0.895,2,2C73.997,73.105,73.104,74.002,71.997,74.002z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M64.141,60.689c-0.781,0.78-2.048,0.78-2.828,0c-0.779-0.781-0.779-2.047,0-2.828l2.828-2.828c0.78-0.78,2.047-0.78,2.828,0c0.78,0.781,0.78,2.047,0,2.828L64.141,60.689z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M50,56.003c-1.104,0-1.999-0.896-1.999-2v-3.999c0-1.104,0.896-2,1.999-2s2,0.896,2,2v3.999C52,55.107,51.104,56.003,50,56.003z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M35.86,60.689l-2.828-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.78,2.047-0.78,2.828,0l2.828,2.828c0.78,0.781,0.78,2.047,0,2.828C37.907,61.47,36.641,61.47,35.86,60.689z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M34.002,72.002c0,1.104-0.896,2-1.999,2h-4c-1.104,0-2-0.896-2-2c0-1.105,0.896-2,2-2h4C33.106,70.002,34.002,70.896,34.002,72.002z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.302,76h-4.381c0.683-1.176,1.078-2.539,1.078-3.998c0-4.418-3.581-8-7.999-8c-4.417,0-7.999,3.582-7.999,8c0,1.459,0.396,2.822,1.079,3.998h-4.381c-0.444-1.252-0.698-2.594-0.698-3.998c0-6.627,5.373-11.999,11.999-11.999c6.627,0,11.999,5.371,11.999,11.999C61.999,73.406,61.745,74.748,61.302,76z"></path>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M40.001,63.998h19.998c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H40.001c-1.104,0-2-0.895-2-2C38.001,64.895,38.897,63.998,40.001,63.998z"></path>
													</g>
												</g>
											</svg> Sunrise Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunriseAltFill" viewbox="15 15 70 70">
												<clippath>
													<rect x="15" y="15" width="70" height="48.999"></rect>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-sunriseAltFill">
													<g clip-path="url(#sunriseFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sunriseAlt">
															<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M72.31,77.999h-3.998c-1.104,0-2-0.896-2-1.999s0.896-2,2-2h3.998c1.104,0,2,0.896,2,2S73.416,77.999,72.31,77.999z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M64.455,64.688c-0.781,0.781-2.05,0.781-2.828,0c-0.781-0.78-0.781-2.047,0-2.828l2.828-2.827c0.778-0.781,2.047-0.781,2.828,0c0.778,0.78,0.778,2.047,0,2.827L64.455,64.688z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M50.313,60.002c-1.104,0-2-0.896-2-2v-3.999c0-1.104,0.896-2,2-2s2,0.896,2,2v3.999C52.313,59.105,51.416,60.002,50.313,60.002z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M36.172,64.688l-2.828-2.828c-0.781-0.78-0.781-2.047,0-2.827c0.781-0.781,2.047-0.781,2.828,0L39,61.859c0.781,0.781,0.781,2.048,0,2.828C38.22,65.469,36.954,65.469,36.172,64.688z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M34.314,76c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.999-0.896-1.999-1.999s0.896-2,1.999-2h4C33.419,74,34.314,74.896,34.314,76z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="50.313" cy="76" r="11.999"></circle>
																<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="50.001" cy="76" r="7.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M40.001,63.998h19.998c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H40.001c-1.104,0-2-0.895-2-2C38.001,64.895,38.897,63.998,40.001,63.998z"></path>
													</g>
												</g>
											</svg> Sunrise Alt Fill
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunsetAlt" viewbox="15 15 70 70">
												<clippath>
													<rect x="15" y="15" width="70" height="45"></rect>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-sunsetAlt">
													<g clip-path="url(#sunriseClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sunsetAlt">
															<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M71.997,74.002h-3.999c-1.104,0-2-0.896-2-2c0-1.105,0.896-2,2-2h3.999c1.104,0,2,0.895,2,2C73.997,73.105,73.104,74.002,71.997,74.002z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M64.141,60.689c-0.781,0.78-2.048,0.78-2.828,0c-0.779-0.781-0.779-2.047,0-2.828l2.828-2.828c0.78-0.78,2.047-0.78,2.828,0c0.78,0.781,0.78,2.047,0,2.828L64.141,60.689z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M50,56.003c-1.104,0-1.999-0.896-1.999-2v-3.999c0-1.104,0.896-2,1.999-2s2,0.896,2,2v3.999C52,55.107,51.104,56.003,50,56.003z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M35.86,60.689l-2.828-2.828c-0.781-0.781-0.781-2.047,0-2.828c0.78-0.78,2.047-0.78,2.828,0l2.828,2.828c0.78,0.781,0.78,2.047,0,2.828C37.907,61.47,36.641,61.47,35.86,60.689z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M34.002,72.002c0,1.104-0.896,2-1.999,2h-4c-1.104,0-2-0.896-2-2c0-1.105,0.896-2,2-2h4C33.106,70.002,34.002,70.896,34.002,72.002z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" d="M61.302,76h-4.381c0.683-1.176,1.078-2.539,1.078-3.998c0-4.418-3.581-8-7.999-8c-4.417,0-7.999,3.582-7.999,8c0,1.459,0.396,2.822,1.079,3.998h-4.381c-0.444-1.252-0.698-2.594-0.698-3.998c0-6.627,5.373-11.999,11.999-11.999c6.627,0,11.999,5.371,11.999,11.999C61.999,73.406,61.745,74.748,61.302,76z"></path>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M40.001,63.998h19.998c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H40.001c-1.104,0-2-0.895-2-2C38.001,64.895,38.897,63.998,40.001,63.998z"></path>
													</g>
												</g>
											</svg> Sunset Alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis svg-icon">
																	<svg version="1.1" class="climacon climacon_sunsetAltFill" viewbox="15 15 70 70">
												<clippath>
													<rect x="15" y="15" width="70" height="48.999"></rect>
												</clippath>
												<g class="climacon_iconWrap climacon_iconWrap-sunsetAltFill">
													<g clip-path="url(#sunriseFillClip)">
														<g class="climacon_componentWrap climacon_componentWrap-sunset climacon_componentWrap-sunsetAlt">
															<g class="climacon_componentWrap climacon_componentWrap-sunSpoke">
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-east" d="M72.31,77.999h-3.998c-1.104,0-2-0.896-2-1.999s0.896-2,2-2h3.998c1.104,0,2,0.896,2,2S73.416,77.999,72.31,77.999z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northEast" d="M64.455,64.688c-0.781,0.781-2.05,0.781-2.828,0c-0.781-0.78-0.781-2.047,0-2.828l2.828-2.827c0.778-0.781,2.047-0.781,2.828,0c0.778,0.78,0.778,2.047,0,2.827L64.455,64.688z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-north" d="M50.313,60.002c-1.104,0-2-0.896-2-2v-3.999c0-1.104,0.896-2,2-2s2,0.896,2,2v3.999C52.313,59.105,51.416,60.002,50.313,60.002z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-northWest" d="M36.172,64.688l-2.828-2.828c-0.781-0.78-0.781-2.047,0-2.827c0.781-0.781,2.047-0.781,2.828,0L39,61.859c0.781,0.781,0.781,2.048,0,2.828C38.22,65.469,36.954,65.469,36.172,64.688z"></path>
																<path class="climacon_component climacon_component-stroke climacon_component-stroke_sunSpoke climacon_component-stroke_sunSpoke-west" d="M34.314,76c0,1.104-0.896,1.999-2,1.999h-4c-1.104,0-1.999-0.896-1.999-1.999s0.896-2,1.999-2h4C33.419,74,34.314,74.896,34.314,76z"></path>
															</g>
															<g class="climacon_wrapperComponent climacon_wrapperComponent-sunBody">
																<circle class="climacon_component climacon_component-stroke climacon_component-stroke_sunBody" cx="50.313" cy="76" r="11.999"></circle>
																<circle class="climacon_component climacon_component-fill climacon_component-fill_sunBody" fill="#FFFFFF" cx="50.001" cy="76" r="7.999"></circle>
															</g>
														</g>
													</g>
													<g class="climacon_wrapperComponent climacon_wrapperComponent-horizonLine">
														<path class="climacon_component climacon_component-stroke climacon_component-stroke_horizonLine" d="M40.001,63.998h19.998c1.104,0,2,0.896,2,2c0,1.105-0.896,2-2,2H40.001c-1.104,0-2-0.895-2-2C38.001,64.895,38.897,63.998,40.001,63.998z"></path>
													</g>
												</g>
											</svg> Sunset Alt Fill
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- SVG Icons Animated CARD end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- 26 NEW 2.0 ICONS card start -->
												<div class="card">
													<div class="card-header">
														<h5>26 New 2.0 Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo ">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-cloudy-high"></i> wi wi-day-cloudy-high
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moonrise"></i>wi wi-moonrise
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-na"></i>wi wi-na
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-volcano"></i>wi wi-volcano
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-light-wind"></i>wi wi-day-light-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moonset"></i>wi wi-moonset
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-flood"></i>wi wi-flood
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-train"></i>wi wi-train
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-sleet"></i>wi wi-day-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-sleet"></i>wi wi-night-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sandstorm"></i>wi wi-sandstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-small-craft-advisory"></i>wi wi-small-craft-advisory
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-haze"></i>wi wi-day-haze
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-sleet"></i>wi wi-night-alt-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-tsunami"></i>wi wi-tsunami
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-gale-warning"></i>wi wi-gale-warning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-cloudy-high"></i>wi wi-night-cloudy-high
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-raindrop"></i>wi wi-raindrop
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-earthquake"></i>wi wi-earthquake
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-storm-warning"></i>wi wi-storm-warning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-partly-cloudy"></i>wi wi-night-alt-partly-cloudy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-barometer"></i>wi wi-barometer
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-fire"></i>wi wi-fire
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-hurricane-warning"></i>wi wi-hurricane-warning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sleet"></i>wi wi-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-humidity"></i>wi wi-humidity
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- 26 NEW 2.0 ICONS card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Daytime icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Daytime Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-sunny"></i> wi wi-day-sunny
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-cloudy"></i> wi wi-day-cloudy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-cloudy-gusts"></i> wi wi-day-cloudy-gusts
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-cloudy-windy"></i> wi wi-day-cloudy-windy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-fog"></i> wi wi-day-fog
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-hail"></i> wi wi-day-hail
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-haze"></i> wi wi-day-haze
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-lightning"></i> wi wi-day-lightning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-rain"></i> wi wi-day-rain
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-rain-mix"></i> wi wi-day-rain-mix
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-rain-wind"></i> wi wi-day-rain-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-showers"></i> wi wi-day-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-sleet"></i> wi wi-day-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-sleet-storm"></i> wi wi-day-sleet-storm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-snow"></i> wi wi-day-snow
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-snow-thunderstorm"></i> wi wi-day-snow-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-snow-wind"></i> wi wi-day-snow-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-sprinkle"></i> wi wi-day-sprinkle
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-storm-showers"></i> wi wi-day-storm-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-sunny-overcast"></i> wi wi-day-sunny-overcast
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-thunderstorm"></i> wi wi-day-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-windy"></i> wi wi-day-windy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-solar-eclipse"></i> wi wi-solar-eclipse
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-hot"></i> wi wi-hot
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-cloudy-high"></i> wi wi-day-cloudy-high
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-day-light-wind"></i> wi wi-day-light-wind
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Daytime icons card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Night time icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Nighttime Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-clear"></i> wi wi-night-clear
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-cloudy"></i> wi wi-night-alt-cloudy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-cloudy-gusts"></i> wi wi-night-alt-cloudy-gusts
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-cloudy-windy"></i> wi wi-night-alt-cloudy-windy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-hail"></i> wi wi-night-alt-hail
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-lightning"></i> wi wi-night-alt-lightning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-rain"></i> wi wi-night-alt-rain
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-rain-mix"></i> wi wi-night-alt-rain-mix
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-rain-wind"></i> wi wi-night-alt-rain-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-showers"></i> wi wi-night-alt-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-sleet"></i> wi wi-night-alt-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-sleet-storm"></i> wi wi-night-alt-sleet-storm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-snow"></i> wi wi-night-alt-snow
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-snow-thunderstorm"></i> wi wi-night-alt-snow-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-snow-wind"></i> wi wi-night-alt-snow-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-sprinkle"></i> wi wi-night-alt-sprinkle
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-storm-showers"></i> wi wi-night-alt-storm-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-thunderstorm"></i> wi wi-night-alt-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-cloudy"></i> wi wi-night-cloudy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-cloudy-gusts"></i> wi wi-night-cloudy-gusts
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-cloudy-windy"></i> wi wi-night-cloudy-windy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-fog"></i> wi wi-night-fog
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-hail"></i> wi wi-night-hail
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-lightning"></i> wi wi-night-lightning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-partly-cloudy"></i> wi wi-night-partly-cloudy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-rain"></i> wi wi-night-rain
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-rain-mix"></i> wi wi-night-rain-mix
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-rain-wind"></i> wi wi-night-rain-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-showers"></i> wi wi-night-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-sleet"></i> wi wi-night-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-sleet-storm"></i> wi wi-night-sleet-storm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-snow"></i> wi wi-night-snow
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-snow-thunderstorm"></i> wi wi-night-snow-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-snow-wind"></i> wi wi-night-snow-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-sprinkle"></i> wi wi-night-sprinkle
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-storm-showers"></i> wi wi-night-storm-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-thunderstorm"></i> wi wi-night-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-lunar-eclipse"></i> wi wi-lunar-eclipse
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-stars"></i> wi wi-stars
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-storm-showers"></i> wi wi-storm-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-thunderstorm"></i> wi wi-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-cloudy-high"></i> wi wi-night-alt-cloudy-high
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-cloudy-high"></i> wi wi-night-cloudy-high
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-night-alt-partly-cloudy"></i> wi wi-night-alt-partly-cloudy
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Night time icons card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Neutral Icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Neutral Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloud"></i> wi wi-cloud
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloudy"></i> wi wi-cloudy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloudy-gusts"></i> wi wi-cloudy-gusts
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloudy-windy"></i> wi wi-cloudy-windy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-fog"></i> wi wi-fog
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-hail"></i> wi wi-hail
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-rain"></i> wi wi-rain
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-rain-mix"></i> wi wi-rain-mix
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-rain-wind"></i> wi wi-rain-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-showers"></i> wi wi-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sleet"></i> wi wi-sleet
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-snow"></i> wi wi-snow
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sprinkle"></i> wi wi-sprinkle
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-storm-showers"></i> wi wi-storm-showers
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-thunderstorm"></i> wi wi-thunderstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-snow-wind"></i> wi wi-snow-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-snow"></i> wi wi-snow
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-smog"></i> wi wi-smog
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-smoke"></i> wi wi-smoke
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-lightning"></i> wi wi-lightning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-raindrops"></i> wi wi-raindrops
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-raindrop"></i> wi wi-raindrop
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-dust"></i> wi wi-dust
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-snowflake-cold"></i> wi wi-snowflake-cold
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-windy"></i> wi wi-windy
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-strong-wind"></i> wi wi-strong-wind
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sandstorm"></i> wi wi-sandstorm
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-earthquake"></i> wi wi-earthquake
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-fire"></i> wi wi-fire
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-flood"></i> wi wi-flood
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-meteor"></i> wi wi-meteor
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-tsunami"></i> wi wi-tsunami
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-volcano"></i> wi wi-volcano
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-hurricane"></i> wi wi-hurricane
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-tornado"></i> wi wi-tornado
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-small-craft-advisory"></i> wi wi-small-craft-advisory
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-gale-warning"></i> wi wi-gale-warning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-storm-warning"></i> wi wi-storm-warning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-hurricane-warning"></i> wi wi-hurricane-warning
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-wind-direction"></i> wi wi-wind-direction
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Neutral Icons card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Miscellaneous icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Miscellaneous Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-alien"></i> wi wi-alien
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-celsius"></i> wi wi-celsius
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-fahrenheit"></i> wi wi-fahrenheit
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-degrees"></i> wi wi-degrees
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-thermometer"></i> wi wi-thermometer
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-thermometer-exterior"></i> wi wi-thermometer-exterior
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-thermometer-internal"></i> wi wi-thermometer-internal
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloud-down"></i> wi wi-cloud-down
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloud-up"></i> wi wi-cloud-up
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-cloud-refresh"></i> wi wi-cloud-refresh
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-horizon"></i> wi wi-horizon
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-horizon-alt"></i> wi wi-horizon-alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sunrise"></i> wi wi-sunrise
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-sunset"></i> wi wi-sunset
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moonrise"></i> wi wi-moonrise
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moonset"></i> wi wi-moonset
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-refresh"></i> wi wi-refresh
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-refresh-alt"></i> wi wi-refresh-alt
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-umbrella"></i> wi wi-umbrella
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-barometer"></i> wi wi-barometer
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-humidity"></i> wi wi-humidity
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-na"></i> wi wi-na
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Miscellaneous icons card start -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Moon Phases Icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Moon Phases Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-new"></i> wi wi-moon-new
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-crescent-1"></i> wi wi-moon-waxing-crescent-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-crescent-2"></i> wi wi-moon-waxing-crescent-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-crescent-3"></i> wi wi-moon-waxing-crescent-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-crescent-4"></i> wi wi-moon-waxing-crescent-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-crescent-5"></i> wi wi-moon-waxing-crescent-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-crescent-6"></i> wi wi-moon-waxing-crescent-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-first-quarter"></i> wi wi-moon-first-quarter
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-gibbous-1"></i> wi wi-moon-waxing-gibbous-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-gibbous-2"></i> wi wi-moon-waxing-gibbous-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-gibbous-3"></i> wi wi-moon-waxing-gibbous-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-gibbous-4"></i> wi wi-moon-waxing-gibbous-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-gibbous-5"></i> wi wi-moon-waxing-gibbous-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waxing-gibbous-6"></i> wi wi-moon-waxing-gibbous-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-full"></i> wi wi-moon-full
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-gibbous-1"></i> wi wi-moon-waning-gibbous-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-gibbous-2"></i> wi wi-moon-waning-gibbous-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-gibbous-3"></i> wi wi-moon-waning-gibbous-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-gibbous-4"></i> wi wi-moon-waning-gibbous-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-gibbous-5"></i> wi wi-moon-waning-gibbous-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-gibbous-6"></i> wi wi-moon-waning-gibbous-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-third-quarter"></i> wi wi-moon-third-quarter
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-crescent-1"></i> wi wi-moon-waning-crescent-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-crescent-2"></i> wi wi-moon-waning-crescent-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-crescent-3"></i> wi wi-moon-waning-crescent-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-crescent-4"></i> wi wi-moon-waning-crescent-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-crescent-5"></i> wi wi-moon-waning-crescent-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-waning-crescent-6"></i> wi wi-moon-waning-crescent-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-new"></i> wi wi-moon-alt-new
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-crescent-1"></i> wi wi-moon-alt-waxing-crescent-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-crescent-2"></i> wi wi-moon-alt-waxing-crescent-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-crescent-3"></i> wi wi-moon-alt-waxing-crescent-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-crescent-4"></i> wi wi-moon-alt-waxing-crescent-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-crescent-5"></i> wi wi-moon-alt-waxing-crescent-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-crescent-6"></i> wi wi-moon-alt-waxing-crescent-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-first-quarter"></i> wi wi-moon-alt-first-quarter
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-gibbous-1"></i> wi wi-moon-alt-waxing-gibbous-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-gibbous-2"></i> wi wi-moon-alt-waxing-gibbous-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-gibbous-3"></i> wi wi-moon-alt-waxing-gibbous-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-gibbous-4"></i> wi wi-moon-alt-waxing-gibbous-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-gibbous-5"></i> wi wi-moon-alt-waxing-gibbous-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waxing-gibbous-6"></i> wi wi-moon-alt-waxing-gibbous-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-full"></i> wi wi-moon-alt-full
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-gibbous-1"></i> wi wi-moon-alt-waning-gibbous-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-gibbous-2"></i> wi wi-moon-alt-waning-gibbous-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-gibbous-3"></i> wi wi-moon-alt-waning-gibbous-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-gibbous-4"></i> wi wi-moon-alt-waning-gibbous-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-gibbous-5"></i> wi wi-moon-alt-waning-gibbous-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-gibbous-6"></i> wi wi-moon-alt-waning-gibbous-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-third-quarter"></i> wi wi-moon-alt-third-quarter
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-crescent-1"></i> wi wi-moon-alt-waning-crescent-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-crescent-2"></i> wi wi-moon-alt-waning-crescent-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-crescent-3"></i> wi wi-moon-alt-waning-crescent-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-crescent-4"></i> wi wi-moon-alt-waning-crescent-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-crescent-5"></i> wi wi-moon-alt-waning-crescent-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-moon-alt-waning-crescent-6"></i> wi wi-moon-alt-waning-crescent-6
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Moon Phases Icons card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Time icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Time Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-1"></i> wi wi-time-1
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-2"></i> wi wi-time-2
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-3"></i> wi wi-time-3
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-4"></i> wi wi-time-4
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-5"></i> wi wi-time-5
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-6"></i> wi wi-time-6
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-7"></i> wi wi-time-7
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-8"></i> wi wi-time-8
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-9"></i> wi wi-time-9
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-10"></i> wi wi-time-10
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-11"></i> wi wi-time-11
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-time-12"></i> wi wi-time-12
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Time icons card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Directional Arrows card start -->
												<div class="card">
													<div class="card-header">
														<h5>Directional Arrows</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-up"></i> wi wi-direction-up
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-up-right"></i> wi wi-direction-up-right
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-right"></i> wi wi-direction-right
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-down-right"></i> wi wi-direction-down-right
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-down"></i> wi wi-direction-down
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-down-left"></i> wi wi-direction-down-left
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-left"></i> wi wi-direction-left
																</div>
																<div class="col-sm-12 col-md-6 col-xl-3 col-lg-4 outer-ellipsis">
																	<i class="wi wi-direction-up-left"></i> wi wi-direction-up-left
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Directional Arrows card end -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Utility Classes card start -->
												<div class="card">
													<div class="card-header">
														<h5>Utility Classes</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
														<div class="data-table-main icon-list-demo">
															<div class="row">
																<div class="col-sm-4">
																	<h5>Flip</h5>
																	<p>
																		<code>wi-flip-horizontal</code>
																	</p>
																	<p>
																		<code>wi-flip-vertical</code>
																	</p>
																</div>
																<div class="col-sm-4">
																	<h5>Rotate</h5>
																	<p>
																		<code>wi-rotate-90</code>
																	</p>
																	<p>
																		<code>wi-rotate-180</code>
																	</p>
																	<p>
																		<code>wi-rotate-270</code>
																	</p>
																</div>
																<div class="col-sm-4">
																	<h5>Fixed Width</h5>
																	<p>
																		<code>wi-fw</code>
																	</p>
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- Utility Classes card start -->
											</div>
										</div>
										<div class="row">
											<div class="col-sm-12">
												<!-- Neutral icons card start -->
												<div class="card">
													<div class="card-header">
														<h5>Neutral Icons</h5>
														<span>lorem ipsum dolor sit amet, consectetur adipisicing elit</span>

													</div>
													<div class="card-block">
													</div>
												</div>
												<!-- Neutral icons card start -->
											</div>
										</div>
									</div>
									<!-- Page body end -->
								</div>
							</div>
							<!-- Main-body end -->

							<div id="styleSelector">

							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>



	<!-- Modal -->
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" id="myModalLabel">Weather Icons</h4>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="row">
						<div class="col-md-12 text-center">
							<div class="icon-list-demo">
								<i id="icon" class="fa fa-wpbeginner fa-lg"></i>
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								Name
								<input class="form-control" type="text" name="Name" id="name" value="fa fa-wpbeginner">
							</div>
							<div class="form-group">
								Code
								<input class="form-control" type="text" id="code" name="Code" value='<i class="fa fa-wpbeginner"></i>'>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>
	<!-- Container-fluid ends -->
	<!-- Warning Section Starts -->
	<!-- Older IE warning message -->
	<!--[if lt IE 10]>
<div class="ie-warning">
	<h1>Warning!!</h1>
	<p>You are using an outdated version of Internet Explorer, please upgrade <br/>to any of the following web browsers to access this website.</p>
	<div class="iew-container">
		<ul class="iew-download">
			<li>
				<a href="http://www.google.com/chrome/">
					<img src="../files/assets/images/browser/chrome.png" alt="Chrome">
					<div>Chrome</div>
				</a>
			</li>
			<li>
				<a href="https://www.mozilla.org/en-US/firefox/new/">
					<img src="../files/assets/images/browser/firefox.png" alt="Firefox">
					<div>Firefox</div>
				</a>
			</li>
			<li>
				<a href="http://www.opera.com">
					<img src="../files/assets/images/browser/opera.png" alt="Opera">
					<div>Opera</div>
				</a>
			</li>
			<li>
				<a href="https://www.apple.com/safari/">
					<img src="../files/assets/images/browser/safari.png" alt="Safari">
					<div>Safari</div>
				</a>
			</li>
			<li>
				<a href="http://windows.microsoft.com/en-us/internet-explorer/download-ie">
					<img src="../files/assets/images/browser/ie.png" alt="">
					<div>IE (9 & above)</div>
				</a>
			</li>
		</ul>
	</div>
	<p>Sorry for the inconvenience!</p>
</div>
<![endif]-->
	<!-- Warning Section Ends -->
	<!-- Required Jquery -->
	<script type="text/javascript" src="libraries\bower_components\jquery\js\jquery.min.js"></script>
	<script type="text/javascript" src="libraries\bower_components\jquery-ui\js\jquery-ui.min.js"></script>
	<script type="text/javascript" src="libraries\bower_components\popper.js\js\popper.min.js"></script>
	<script type="text/javascript" src="libraries\bower_components\bootstrap\js\bootstrap.min.js"></script>
	<!-- jquery slimscroll js -->
	<script type="text/javascript" src="libraries\bower_components\jquery-slimscroll\js\jquery.slimscroll.js"></script>
	<!-- modernizr js -->
	<script type="text/javascript" src="libraries\bower_components\modernizr\js\modernizr.js"></script>
	<script type="text/javascript" src="libraries\bower_components\modernizr\js\css-scrollbars.js"></script>
	<!-- classie js -->
	<script type="text/javascript" src="../files/bower_components/classie/js/classie.js"></script>
	<!-- i18next.min.js -->
	<script type="text/javascript" src="libraries\bower_components\i18next\js\i18next.min.js"></script>
	<script type="text/javascript" src="libraries\bower_components\i18next-xhr-backend\js\i18nextXHRBackend.min.js"></script>
	<script type="text/javascript" src="libraries\bower_components\i18next-browser-languagedetector\js\i18nextBrowserLanguageDetector.min.js"></script>
	<script type="text/javascript" src="libraries\bower_components\jquery-i18next\js\jquery-i18next.min.js"></script>
	<!-- Custom js -->
	<script type="text/javascript" src="libraries\assets\icon\weather-icons\weather-custom.js"></script>

	<script src="libraries\assets\js\pcoded.min.js"></script>
	<script src="libraries\assets\js\vartical-layout.min.js"></script>
	<script src="libraries\assets\js\jquery.mCustomScrollbar.concat.min.js"></script>
	<script type="text/javascript" src="libraries\assets\js\script.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-23581568-13"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-23581568-13');
</script>
</body>

</html>
