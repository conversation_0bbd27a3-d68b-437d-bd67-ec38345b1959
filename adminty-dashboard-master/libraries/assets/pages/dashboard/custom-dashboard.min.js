"use strict";$(document).ready(function(){function e(e,a,r){return null==r&&(r="rgba(0,0,0,0)"),{labels:["1","2","3","4","5"],datasets:[{label:"",borderColor:e,borderWidth:0,hitRadius:30,pointRadius:0,pointHoverRadius:4,pointBorderWidth:2,pointHoverBorderWidth:12,pointBackgroundColor:Chart.helpers.color("#000000").alpha(0).rgbString(),pointBorderColor:e,pointHoverBackgroundColor:e,pointHoverBorderColor:Chart.helpers.color("#000000").alpha(.1).rgbString(),fill:!0,backgroundColor:Chart.helpers.color(r).alpha(1).rgbString(),data:a}]}}function a(){return{maintainAspectRatio:!1,title:{display:!1},tooltips:{enabled:!1},legend:{display:!1},hover:{mode:"index"},scales:{xAxes:[{display:!1,gridLines:!1,scaleLabel:{display:!0,labelString:"Month"}}],yAxes:[{display:!1,gridLines:!1,scaleLabel:{display:!0,labelString:"Value"},ticks:{min:1}}]},elements:{point:{radius:4,borderWidth:12}},layout:{padding:{left:10,right:0,top:15,bottom:0}}}}function r(e,a,r){return null==r&&(r="rgba(0,0,0,0)"),{labels:["January","February","March","April","May","June","July","August","September","October"],datasets:[{label:"",borderColor:e,borderWidth:2,hitRadius:30,pointHoverRadius:4,pointBorderWidth:50,pointHoverBorderWidth:12,pointBackgroundColor:Chart.helpers.color("#000000").alpha(0).rgbString(),pointBorderColor:Chart.helpers.color("#000000").alpha(0).rgbString(),pointHoverBackgroundColor:e,pointHoverBorderColor:Chart.helpers.color("#000000").alpha(.1).rgbString(),fill:!0,backgroundColor:r,data:a}]}}function t(){return{maintainAspectRatio:!1,title:{display:!1},tooltips:{enabled:!1},legend:{display:!1,labels:{usePointStyle:!1}},responsive:!0,maintainAspectRatio:!0,hover:{mode:"index"},scales:{xAxes:[{display:!1,gridLines:!1,scaleLabel:{display:!0,labelString:"Month"}}],yAxes:[{display:!1,gridLines:!1,scaleLabel:{display:!0,labelString:"Value"},ticks:{beginAtZero:!0}}]},elements:{point:{radius:4,borderWidth:12}},layout:{padding:{left:0,right:0,top:5,bottom:0}}}}var l=document.getElementById("update-chart-1").getContext("2d"),l=(new Chart(l,{type:"bar",data:e("#fff",[25,30,20,15,20],"#fff"),options:a()}),document.getElementById("update-chart-2").getContext("2d")),l=(new Chart(l,{type:"bar",data:e("#fff",[10,30,20,15,30],"#fff"),options:a()}),document.getElementById("update-chart-3").getContext("2d")),l=(new Chart(l,{type:"bar",data:e("#fff",[25,10,20,15,20],"#fff"),options:a()}),document.getElementById("update-chart-4").getContext("2d"));new Chart(l,{type:"bar",data:e("#fff",[25,30,20,15,10],"#fff"),options:a()});$(function(){function e(){a.zoomToIndexes(Math.round(.4*a.dataProvider.length),Math.round(.55*a.dataProvider.length))}var a=AmCharts.makeChart("sales-analytics",{type:"serial",theme:"light",marginTop:0,marginRight:0,dataProvider:[{year:"1950",value:-.307},{year:"1951",value:-.168},{year:"1952",value:-.073},{year:"1953",value:-.027},{year:"1954",value:-.251},{year:"1955",value:-.281},{year:"1956",value:-.348},{year:"1957",value:-.074},{year:"1958",value:-.011},{year:"1959",value:-.074},{year:"1960",value:-.124},{year:"1961",value:-.024},{year:"1962",value:-.022},{year:"1963",value:0},{year:"1964",value:-.296},{year:"1965",value:-.217},{year:"1966",value:-.147},{year:"1967",value:-.15},{year:"1968",value:-.16},{year:"1969",value:-.011},{year:"1970",value:-.068},{year:"1971",value:-.19},{year:"1972",value:-.056},{year:"1973",value:.077},{year:"1974",value:-.213},{year:"1975",value:-.17},{year:"1976",value:-.254},{year:"1977",value:.019},{year:"1978",value:-.063},{year:"1979",value:.05},{year:"1980",value:.077},{year:"1981",value:.12},{year:"1982",value:.011},{year:"1983",value:.177},{year:"1984",value:-.021},{year:"1985",value:-.037},{year:"1986",value:.03},{year:"1987",value:.179},{year:"1988",value:.18},{year:"1989",value:.104},{year:"1990",value:.255},{year:"1991",value:.21},{year:"1992",value:.065},{year:"1993",value:.11},{year:"1994",value:.172},{year:"1995",value:.269},{year:"1996",value:.141},{year:"1997",value:.353},{year:"1998",value:.548},{year:"1999",value:.298},{year:"2000",value:.267},{year:"2001",value:.411},{year:"2002",value:.462},{year:"2003",value:.47},{year:"2004",value:.445},{year:"2005",value:.47}],valueAxes:[{axisAlpha:0,gridAlpha:0,position:"left"}],graphs:[{id:"g1",balloonText:"[[category]]<br><b><span style='font-size:14px;'>[[value]]</span></b>",bullet:"round",bulletSize:8,lineColor:"#fe5d70",lineThickness:2,negativeLineColor:"#fe9365",type:"smoothedLine",valueField:"value"}],chartScrollbar:{graph:"g1",gridAlpha:0,color:"#888888",scrollbarHeight:55,backgroundAlpha:0,selectedBackgroundAlpha:.1,selectedBackgroundColor:"#888888",graphFillAlpha:0,autoGridCount:!0,selectedGraphFillAlpha:0,graphLineAlpha:.2,graphLineColor:"#c2c2c2",selectedGraphLineColor:"#888888",selectedGraphLineAlpha:1},chartCursor:{categoryBalloonDateFormat:"YYYY",cursorAlpha:0,valueLineEnabled:!0,valueLineBalloonEnabled:!0,valueLineAlpha:.5,fullWidth:!0},dataDateFormat:"YYYY",categoryField:"year",categoryAxis:{minPeriod:"YYYY",parseDates:!0,gridAlpha:0,minorGridAlpha:0,minorGridEnabled:!0},export:{enabled:!0}});a.addListener("rendered",e),a.zoomChart&&a.zoomChart()});var l=document.getElementById("app-sale1").getContext("2d"),l=(new Chart(l,{type:"line",data:r("#11c15b",[1,15,30,15,25,35,45,20,25,30],"transparent"),options:t()}),document.getElementById("app-sale2").getContext("2d")),l=(new Chart(l,{type:"line",data:r("#448aff",[45,30,25,35,20,35,45,20,25,1],"transparent"),options:t()}),document.getElementById("app-sale3").getContext("2d")),l=(new Chart(l,{type:"line",data:r("#ff5252",[1,45,24,40,20,35,10,20,45,30],"transparent"),options:t()}),document.getElementById("app-sale4").getContext("2d"));new Chart(l,{type:"line",data:r("#536dfe",[1,15,45,15,25,35,45,20,25,30],"transparent"),options:t()})});