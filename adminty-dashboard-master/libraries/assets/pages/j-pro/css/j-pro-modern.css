﻿/* Font
=============================== */
@import url(../../../../../../../css-7.css);

/* Default
=============================== */
.j-wrapper {
	margin:0 auto;
	outline:none;
	padding:40px 15px;
	-webkit-box-sizing:content-box;
	-moz-box-sizing:content-box;
	box-sizing:content-box;
}
.j-wrapper-400 { max-width:400px; }

.j-wrapper-640 { max-width:640px; }

.j-pro {
	background-color:#f9fafd;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	-webkit-box-shadow:0 2px 5px rgba(0,0,0,.6);
	-moz-box-shadow:0 2px 5px rgba(0,0,0,.6);
	-o-box-shadow:0 2px 5px rgba(0,0,0,.6);
	box-shadow:0 2px 5px rgba(0,0,0,.6);
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	color:rgba(0,0,0,.54);
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	line-height:1;
	position:relative;
}
.j-pro .j-input { position:relative; }

.j-pro .j-unit { position:relative; margin-bottom:25px; }

.j-pro .j-link {
	border-bottom:1px solid #90caf9;
	color:#1e88e5;
	font-size:14px;
	line-height:inherit;
	text-decoration:none;
}
.j-pro .j-link:hover { border-bottom:none; }

.j-pro .j-inline-group { display:inline-block; }

.j-hidden,
.j-pro .j-token,
.j-pro .j-hidden,
.j-pro .j-hidden input,
.j-pro .j-hidden select,
.j-pro .j-hidden textarea,
.j-pro .j-hiddenBtn { display:none !important; }

/* Reset for -webkit / -moz browser
=============================== */
.j-pro input[type="search"]::-webkit-search-decoration,
.j-pro input[type="search"]::-webkit-search-cancel-button,
.j-pro input[type="search"]::-webkit-search-results-button,
.j-pro input[type="search"]::-webkit-search-results-decoration { display:none; }

.j-pro select,
.j-pro input[type="button"],
.j-pro input[type="submit"],
.j-pro input[type="search"] {
	-webkit-tap-highlight-color:transparent;
	-webkit-tap-highlight-color:rgba(0,0,0,0);
	-webkit-appearance:none;
	-moz-appearance:none;
	appearance:none;
	-webkit-border-radius:0px;
	border-radius:0px;
}

/* Header
=============================== */
.j-pro .j-header {
	background-color:#3f51b5;
	border-top:1px solid #7986cb;
	-webkit-border-radius:3px 3px 0 0;
	-moz-border-radius:3px 3px 0 0;
	-o-border-radius:3px 3px 0 0;
	border-radius:3px 3px 0 0;
	-webkit-box-shadow:0 6px 3px -3px rgba(63,81,181,.5);
	-moz-box-shadow:0 6px 3px -3px rgba(63,81,181,.5);
	-o-box-shadow:0 6px 3px -3px rgba(63,81,181,.5);
	box-shadow:0 6px 3px -3px rgba(63,81,181,.5);
	display:block;
	position:relative;
}
.j-pro .j-header > i { color:#fff; font-size:31px; float:left; padding:31px 15px 0 25px; }

.j-pro .j-header p {
	color:#fff;
	margin:0;
	padding:30px 25px;
	font-size:30px;
	text-transform:uppercase;
}

/* Content
=============================== */
.j-pro .j-content { padding:25px 25px 0; }

.j-pro .j-content:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

/* Footer
=============================== */
.j-pro .j-footer {
	background-color:#e8eaf6;
	border-top:1px solid #303f9f;
	-webkit-border-radius:0 0 3px 3px;
	-moz-border-radius:0 0 3px 3px;
	-o-border-radius:0 0 3px 3px;
	border-radius:0 0 3px 3px;
	display:block;
	padding:10px 25px;
}
.j-pro .j-footer:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

/* Response block
=============================*/
.j-pro .j-response {
	opacity:0;
	visibility:hidden;
	-webkit-transition:padding-top .2s, padding-bottom .2s, opacity .5s;
	-moz-transition:padding-top .2s, padding-bottom .2s, opacity .5s;
	-ms-transition:padding-top .2s, padding-bottom .2s, opacity .5s;
	-o-transition:padding-top .2s, padding-bottom .2s, opacity .5s;
	transition:padding-top .2s, padding-bottom .2s, opacity .5s;
}
.j-pro .j-response.j-success-message,
.j-pro .j-response.j-error-message { opacity:1; visibility:visible; }

/* Dividers
=============================== */
.j-pro .j-divider,
.j-pro .j-divider-text { border-top:1px solid rgba(0,0,0,.12); height:0; }

.j-pro .j-divider-text { text-align:center; }

.j-pro .j-divider-text span {
	border:1px solid rgba(0,0,0,.12);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	background-color:#f9fafd;
	color:#3f51b5;
	font-size:16px;
	padding:2px 15px;
	position:relative;
	top:-9px;
	white-space:nowrap;
}

/* Gap-top / gap-bottom classes
=============================== */
.j-pro .j-gap-top-20 { margin-top:20px; } /* text-divider top gap after "content"/"j-row" classes */

.j-pro .j-gap-top-45 { margin-top:45px; } /* text-divider top gap after "unit" class */

.j-pro .j-gap-bottom-45 { margin-bottom:45px; } /* text-divider bottom gap */

.j-pro .j-gap-bottom-25 { margin-bottom:25px; } /* line-divider bottom gap */

/* Labels
=============================== */
.j-pro label {
	display:block;
	color:inherit;
	font-weight:normal;
	text-align:left;
	margin-bottom:0;
}
.j-pro .j-label { font-size:14px; margin-bottom:6px; line-height:14px; height:14px; }

.j-pro .j-label-center { height:48px; line-height:48px; text-align:center; margin-bottom:0; }

.j-pro .j-row > .j-label{ padding-left:10px; }

/* Radio and checkbox
=============================== */
.j-pro .j-radio,
.j-pro .j-checkbox,
.j-pro .j-radio-toggle,
.j-pro .j-checkbox-toggle {
	color:rgba(0,0,0,.87);
	cursor:pointer;
	font-size:15px;
	height:15px;
	margin-bottom:4px;
	position:relative;
	line-height:15px;
}
.j-pro .j-radio,
.j-pro .j-checkbox,
.j-pro .j-inline-group .j-radio,
.j-pro .j-inline-group .j-checkbox { padding:9px 0 8px 32px; }

.j-pro .j-radio-toggle,
.j-pro .j-checkbox-toggle,
.j-pro .j-inline-group .j-radio-toggle,
.j-pro .j-inline-group .j-checkbox-toggle { padding:9px 0 8px 58px; }

.j-pro .j-radio:last-child,
.j-pro .j-checkbox:last-child,
.j-pro .j-radio-toggle:last-child,
.j-pro .j-checkbox-toggle:last-child { margin-bottom:0; }

.j-pro .j-inline-group .j-radio,
.j-pro .j-inline-group .j-checkbox,
.j-pro .j-inline-group .j-radio-toggle,
.j-pro .j-inline-group .j-checkbox-toggle { display:inline-block; margin-right:25px; }

.j-pro .j-radio input,
.j-pro .j-checkbox input,
.j-pro .j-radio-toggle input,
.j-pro .j-checkbox-toggle input { position:absolute; left:-9999px; }

.j-pro .j-radio i,
.j-pro .j-checkbox i,
.j-pro .j-checkbox-toggle i,
.j-pro .j-radio-toggle i {
	background-color:#fff;
	border:2px solid rgba(0,0,0,.26);
	display:block;
	height:18px;
	left:0;
	outline:none;
	position:absolute;
	top:5px;
	-webkit-transition:border-color.2s;
	-moz-transition:border-color.2s;
	-ms-transition:border-color.2s;
	-o-transition:border-color.2s;
	transition:border-color.2s;
}
.j-pro .j-radio i,
.j-pro .j-checkbox i { width:18px; }

.j-pro .j-checkbox-toggle i,
.j-pro .j-radio-toggle i { width:44px; }

.j-pro .j-checkbox i,
.j-pro .j-checkbox-toggle i {
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
}
.j-pro .j-radio i,
.j-pro .j-radio i:after,
.j-pro .j-radio-toggle i:before {
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	-o-border-radius:50%;
	border-radius:50%;
}
.j-pro .j-radio-toggle i {
	-webkit-border-radius:13px;
	-moz-border-radius:13px;
	-o-border-radius:13px;
	border-radius:13px;
}
.j-pro .j-checkbox-toggle i:before {
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
}
.j-pro .j-radio i:after {
	background-color:rgba(48,63,159,.9);
	content:"";
	height:8px;
	top:5px;
	left:5px;
	opacity:0;
	position:absolute;
	width:8px;
}
.j-pro .j-checkbox i:after {
	border-width:0 0 3px 3px;
	border-bottom:solid rgb(48,63,159);
	border-left:solid rgb(48,63,159);
	content:"";
	height:5px;
	top:3px;
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
	left:3px;
	opacity:0;
	position:absolute;
	width:10px;
}
.j-pro .j-radio input:checked + i:after,
.j-pro .j-checkbox input:checked + i:after { opacity:1; }

.j-pro .j-checkbox-toggle i:before,
.j-pro .j-radio-toggle i:before {
	border:none;
	background-color:rgba(48,63,159,.9);
	content:"";
	display:block;
	height:14px;
	left:2px;
	position:absolute;
	top:2px;
	width:14px;
}
.j-pro .j-checkbox-toggle input:checked + i:before,
.j-pro .j-radio-toggle input:checked + i:before { left:28px; }

.j-pro .j-checkbox-toggle i:after,
.j-pro .j-radio-toggle i:after,
.j-pro .j-checkbox-toggle input:checked + i:after,
.j-pro .j-radio-toggle input:checked + i:after {
	font-size:10px;
	font-style:normal;
	font-weight:bold;
	line-height:10px;
	position:absolute;
	top:4px;
}
.j-pro .j-checkbox-toggle i:after,
.j-pro .j-radio-toggle i:after { content:"NO"; left:22px; }

.j-pro .j-checkbox-toggle input:checked + i:after,
.j-pro .j-radio-toggle input:checked + i:after { content:"YES"; left:6px; }

.j-pro .j-checkbox:hover i,
.j-pro .j-radio:hover i,
.j-pro .j-checkbox-toggle:hover i,
.j-pro .j-radio-toggle:hover i { border:2px solid rgba(48,63,159,.6); }

.j-pro .j-radio input:checked + i,
.j-pro .j-checkbox input:checked + i,
.j-pro .j-radio-toggle input:checked + i,
.j-pro .j-checkbox-toggle input:checked + i { border:2px solid rgba(48,63,159,.9); }

.j-pro .j-radio input:checked + i,
.j-pro .j-checkbox input:checked + i { color:rgba(48,63,159,.9); }

.j-pro .j-checkbox-toggle input:checked + i,
.j-pro .j-radio-toggle input:checked + i { background-color:#e8eaf6; }

/* Widget
=============================== */
.j-pro .j-widget { position: relative; }

.j-pro .j-widget .j-addon,
.j-pro .j-widget .j-addon-btn {
	border:none;
	display:block;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	height:48px;
	line-height:48px;
	padding:0;
	position:absolute;
	outline:none;
	overflow:hidden;
	text-align:center;
	top:0;
	z-index:5;
}
.j-pro .j-widget .j-addon {
	background:#e0e0e0;
	color:rgba(0,0,0,.56);
}
.j-pro .j-widget .j-addon-btn {
	background:#303f9f;
	color:#fff;
}
.j-pro .j-widget .j-addon-btn,
.j-pro .j-widget .j-addon-btn i {
	cursor:pointer;
	-webkit-transition:all.2s;
	-moz-transition:all.2s;
	-ms-transition:all.2s;
	-o-transition:all.2s;
	transition:all.2s;
}
.j-pro .j-widget .j-addon-btn:hover,
.j-pro .j-widget .j-addon-btn:focus { background-color:#3f51b5; }

.j-pro .j-widget .j-adn-left { left:0; }

.j-pro .j-widget .j-adn-right { right:0; }

.j-pro .j-widget .j-addon i { color:rgba(0,0,0,.34); font-size:17px; z-index:2; }

.j-pro .j-widget .j-addon-btn i { color:#fff; font-size:17px; z-index:2; }

.j-pro .j-widget .j-adn-50 { width:50px; }

.j-pro .j-widget .j-adn-130 { width:130px; }

.j-pro .j-widget.j-right-50 .j-input { padding-right:50px; }

.j-pro .j-widget.j-left-50 .j-input { padding-left:50px; }

.j-pro .j-widget.j-right-130 .j-input { padding-right:130px; }

.j-pro .j-widget.j-left-130 .j-input { padding-left:130px; }

.j-pro .j-widget .j-adn-left,
.j-pro .j-widget.j-right-50 .j-input input,
.j-pro .j-widget.j-right-130 .j-input input {
	-webkit-border-radius:3px 0 0 3px;
	-moz-border-radius:3px 0 0 3px;
	-o-border-radius:3px 0 0 3px;
	border-radius:3px 0 0 3px;
}
.j-pro .j-widget .j-adn-right,
.j-pro .j-widget.j-left-50 .j-input input,
.j-pro .j-widget.j-left-130 .j-input input {
	-webkit-border-radius:0 3px 3px 0;
	-moz-border-radius:0 3px 3px 0;
	-o-border-radius:0 3px 3px 0;
	border-radius:0 3px 3px 0;
}
.j-pro .j-widget.j-left-50.j-right-50 .j-input input,
.j-pro .j-widget.j-left-50.j-right-130 .j-input input,
.j-pro .j-widget.j-left-130.j-right-50 .j-input input,
.j-pro .j-widget.j-left-130.j-right-130 .j-input input {
	-webkit-border-radius:0;
	-moz-border-radius:0;
	-o-border-radius:0;
	border-radius:0;
}

/* Inputs
=============================== */
.j-pro input[type="text"],
.j-pro input[type="password"],
.j-pro input[type="email"],
.j-pro input[type="search"],
.j-pro input[type="url"],
.j-pro textarea,
.j-pro select {
	background:#fff;
	border:2px solid rgba(0,0,0,.12);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	color:rgba(0,0,0,.87);
	display:block;
	font-family:inherit;
	font-size:16px;
	height:48px;
	padding:10px 15px;
	width:100%;
	outline:none;
	-webkit-appearance:none;
	-moz-appearance:none;
	appearance:none;
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	-webkit-transition:all.4s;
	-moz-transition:all.4s;
	-ms-transition:all.4s;
	-o-transition:all.4s;
	transition:all.4s;
}
.j-pro input[type="text"]:hover,
.j-pro input[type="password"]:hover,
.j-pro input[type="email"]:hover,
.j-pro input[type="search"]:hover,
.j-pro input[type="url"]:hover,
.j-pro textarea:hover,
.j-pro select:hover { border:2px solid rgba(48,63,159,.6); }

.j-pro input[type="text"]:focus,
.j-pro input[type="password"]:focus,
.j-pro input[type="email"]:focus,
.j-pro input[type="search"]:focus,
.j-pro input[type="url"]:focus,
.j-pro textarea:focus,
.j-pro select:focus { border:2px solid rgba(48,63,159,.9); }

.j-pro .j-input textarea { height:112px; overflow:auto; min-height:52px; resize:vertical; }

.j-pro .j-input textarea:focus { height:128px; }

/* Placeholders
=============================== */
.j-pro input::-webkit-input-placeholder,
.j-pro textarea::-webkit-input-placeholder { color:rgba(0,0,0,.54); }

.j-pro input::-moz-placeholder,
.j-pro textarea::-moz-placeholder { color:rgba(0,0,0,.54); }

.j-pro input:-moz-placeholder,
.j-pro textarea:-moz-placeholder { color:rgba(0,0,0,.54); }

.j-pro input:-ms-input-placeholder,
.j-pro textarea:-ms-input-placeholder { color:rgba(0,0,0,.54); }

.j-pro input:focus::-webkit-input-placeholder,
.j-pro textarea:focus::-webkit-input-placeholder { color:rgba(0,0,0,.36); }

.j-pro input:focus::-moz-placeholder,
.j-pro textarea:focus::-moz-placeholder { color:rgba(0,0,0,.36); }

.j-pro input:focus:-moz-placeholder,
.j-pro textarea:focus:-moz-placeholder { color:rgba(0,0,0,.36); }

.j-pro input:focus:-ms-input-placeholder,
.j-pro textarea:focus:-ms-input-placeholder { color:rgba(0,0,0,.36); }

/* Select
=============================== */
.j-pro select { padding-left:13px; }

.j-pro .j-multiple-select select { height:auto; }

.j-pro .j-select i {
	background:#fff;
	-webkit-box-shadow:0 0 0 11px #fff;
	-moz-box-shadow:0 0 0 11px #fff;
	-o-box-shadow:0 0 0 11px #fff;
	box-shadow:0 0 0 11px #fff;
	height:20px;
	position:absolute;
	pointer-events:none;
	top:14px;
	right:14px;
	width:14px;
}
.j-pro .j-select i:after,
.j-pro .j-select i:before {
	border-right:4px solid transparent;
	border-left:4px solid transparent;
	content:'';
	position:absolute;
	right:3px;
}
.j-pro .j-select i:after { border-top:6px solid rgba(0,0,0,.4); bottom:1px; }

.j-pro .j-select i:before { border-bottom:6px solid rgba(0,0,0,.4); top:3px; }

.j-pro .j-select { position:relative; }

/* Icons
=============================== */
.j-pro .j-icon-left,
.j-pro .j-icon-right {
	color:rgba(0,0,0,.54);
	font-size:17px;
	height:38px;
	line-height:38px !important;
	opacity:.6;
	position:absolute;
	text-align:center;
	top:5px;
	width:42px;
	z-index:2;
}
.j-pro .j-icon-left { border-right:1px solid rgba(0,0,0,.54); left:3px; }

.j-pro .j-icon-right { border-left:1px solid rgba(0,0,0,.54); right:3px; }

.j-pro .j-icon-left ~ input,
.j-pro .j-icon-left ~ textarea { padding-left:58px; }

.j-pro .j-icon-right ~ input,
.j-pro .j-icon-right ~ textarea { padding-right:58px; }

/* File for upload
=============================== */
.j-pro .j-file-button input {
	bottom:-1px;
	font-size:34px;
	opacity:0;
	position:absolute;
	width:108px;
	z-index:0;
}
.j-pro .j-prepend-small-btn .j-file-button input,
.j-pro .j-prepend-big-btn .j-file-button input { left:0; }

.j-pro .j-append-small-btn .j-file-button input,
.j-pro .j-append-big-btn .j-file-button input { right:0; }

.j-pro .j-prepend-small-btn .j-file-button,
.j-pro .j-append-small-btn .j-file-button { width:64px; }

.j-pro .j-prepend-big-btn .j-file-button,
.j-pro .j-append-big-btn .j-file-button { width:106px; }

.j-pro .j-prepend-small-btn .j-file-button,
.j-pro .j-prepend-big-btn .j-file-button { left:4px; }

.j-pro .j-append-small-btn .j-file-button,
.j-pro .j-append-big-btn .j-file-button { right:4px; }

.j-pro .j-append-small-btn .j-file-button,
.j-pro .j-append-big-btn .j-file-button,
.j-pro .j-prepend-small-btn .j-file-button,
.j-pro .j-prepend-big-btn .j-file-button {
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
}
.j-pro .j-prepend-big-btn input[type="text"] { padding-left:123px; }

.j-pro .j-append-big-btn input[type="text"] { padding-right:123px; }

.j-pro .j-prepend-small-btn input[type="text"] { padding-left:81px; }

.j-pro .j-append-small-btn input[type="text"] { padding-right:81px; }

.j-pro .j-input input[type="file"] { cursor:pointer; }

/* Buttons
=============================== */
.j-pro .j-primary-btn,
.j-pro .j-secondary-btn {
	border:none;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	color:#fff;
	display:block;
	cursor:pointer;
	float:right;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	height:48px;
	margin:10px 0 10px 20px;
	outline:none;
	padding:0 25px;
	white-space:nowrap;
}
.j-pro .j-primary-btn { position:relative; }

.j-pro .j-content .j-primary-btn,
.j-pro .j-content .j-secondary-btn { margin:0 0 20px 20px; }

.j-pro .j-file-button {
	color:#fff;
	display:block;
	font-family:'Open Sans',Helvetica,Arial,sans-serif;
	font-size:14px;
	height:40px;
	line-height:40px;
	outline:none;
	overflow:hidden;
	position:absolute;
	text-align:center;
	top:4px;
	z-index:1;
}
.j-pro .j-primary-btn,
.j-pro .j-file-button,
.j-pro .j-secondary-btn {
	background:#303f9f;
	-webkit-transition:background.2s;
	-moz-transition:background.2s;
	-ms-transition:background.2s;
	-o-transition:background.2s;
	transition:background.2s;
}
.j-pro .j-primary-btn:hover,
.j-pro .j-file-button:hover,
.j-pro .j-secondary-btn:hover { background:#3f51b5; }

.j-pro .j-primary-btn:hover.j-processing { background:#303f9f; cursor:wait; }

.j-pro .j-file-button:hover + input { border:2px solid rgba(48,63,159,.6); }

.j-pro .j-secondary-btn,
.j-pro .j-secondary-btn:hover,
.j-pro .j-secondary-btn:active { opacity:.5; }

.j-pro .j-primary-btn.j-processing:before {
	background:rgba(255,255,255,.4);
	content:'';
	height:100%;
	position:absolute;
	top:0;
	left:0;
	width:100%;
	-webkit-animation:j-processing 3s ease-in-out infinite;
	-moz-animation:j-processing 3s ease-in-out infinite;;
	-ms-animation:j-processing 3s ease-in-out infinite;
	-o-animation:j-processing 3s ease-in-out infinite;
	animation:j-processing 3s ease-in-out infinite;
}
@-webkit-keyframes j-processing {
	0% { width:0; }
	100% { width:100%; }
}
@-moz-keyframes j-processing {
	0% { width:0; }
	100% { width:100%; }
}
@-ms-keyframes j-processing {
	0% { width:0; }
	100% { width:100%; }
}
@-o-keyframes j-processing {
	0% { width:0; }
	100% { width:100%; }
}
@keyframes j-processing {
	0% { width:0; }
	100% { width:100%; }
}

/* Tooltip
=============================== */
.j-pro .j-tooltip,
.j-pro .j-tooltip-image {
	background-color:#1a237e;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	display:block;
	left:-9999px;
	opacity:0;
	position:absolute;
	z-index:0;
}
.j-pro .j-tooltip {
	color:#fff;
	font:600 13px 'Open Sans',Helvetica,Arial,sans-serif;
	line-height:20px;
	padding:5px 10px;
}
.j-pro .j-tooltip-image { padding:2px 2px 1px; }

.j-pro .j-input input:focus + .j-tooltip,
.j-pro .j-input textarea:focus + .j-tooltip,
.j-pro .j-select select:focus + .j-tooltip,
.j-pro .j-input input:focus + .j-tooltip-image,
.j-pro .j-input textarea:focus + .j-tooltip-image,
.j-pro .j-select select:focus + .j-tooltip-image { opacity:1; z-index:5; }

.j-pro .j-tooltip-left-top { bottom:100%; margin-bottom:8px; }

.j-pro .j-tooltip-left-top:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:8px 7px 0;
	bottom:-6px;
	content:"";
	left:16px;
	position:absolute;
}
.j-pro .j-input input:focus + .j-tooltip-left-top,
.j-pro .j-input textarea:focus + .j-tooltip-left-top,
.j-pro .j-select select:focus + .j-tooltip-left-top { left:0; right:auto; }

.j-pro .j-tooltip-right-top { bottom:100%; margin-bottom:8px; }

.j-pro .j-tooltip-right-top:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:8px 7px 0;
	bottom:-6px;
	content:"";
	position:absolute;
	right:16px;
}
.j-pro .j-input input:focus + .j-tooltip-right-top,
.j-pro .j-input textarea:focus + .j-tooltip-right-top,
.j-pro .j-select select:focus + .j-tooltip-right-top { left:auto; right:0; }

.j-pro .j-tooltip-left-bottom { margin-top:8px; top:100%; }

.j-pro .j-tooltip-left-bottom:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:0 7px 8px;
	top:-6px;
	content:"";
	left:16px;
	position:absolute;
}
.j-pro .j-input input:focus + .j-tooltip-left-bottom,
.j-pro .j-input textarea:focus + .j-tooltip-left-bottom,
.j-pro .j-select select:focus + .j-tooltip-left-bottom { left:0; right:auto; }

.j-pro .j-tooltip-right-bottom { margin-top:8px; top:100%; }

.j-pro .j-tooltip-right-bottom:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:0 7px 8px;
	top:-6px;
	content:"";
	right:16px;
	position:absolute;
}
.j-pro .j-input input:focus + .j-tooltip-right-bottom,
.j-pro .j-input textarea:focus + .j-tooltip-right-bottom,
.j-pro .j-select select:focus + .j-tooltip-right-bottom { left:auto; right:0; }

.j-pro .j-tooltip-right-side { margin-left:8px; top:8px; white-space:nowrap; }

.j-pro .j-tooltip-right-side:before {
	border-color:transparent #1a237e;
	border-style:solid;
	border-width:7px 8px 7px 0;
	content:"";
	left:-6px;
	position:absolute;
	top:8px;
}
.j-pro .j-input input:focus + .j-tooltip-right-side,
.j-pro .j-input textarea:focus + .j-tooltip-right-side,
.j-pro .j-select select:focus + .j-tooltip-right-side { left:100%; }

.j-pro .j-tooltip-left-side { margin-right:8px; top:8px; white-space:nowrap; }

.j-pro .j-tooltip-left-side:before {
	border-color:transparent #1a237e;
	border-style:solid;
	border-width:7px 0 7px 8px;
	content:"";
	right:-6px;
	position:absolute;
	top:8px;
}
.j-pro .j-input input:focus + .j-tooltip-left-side,
.j-pro .j-input textarea:focus + .j-tooltip-left-side,
.j-pro .j-select select:focus + .j-tooltip-left-side { left:auto; right:100%; }

/* Status message
=============================== */
.j-pro .j-error-message,
.j-pro .j-success-message,
.j-pro .j-info-message,
.j-pro .j-warning-message {
	border:2px solid;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	display:block;
	font:16px/24px 'Open Sans',Helvetica,Arial,sans-serif;
	padding:15px;
}
.j-pro .j-error-message i,
.j-pro .j-success-message i,
.j-pro .j-info-message i,
.j-pro .j-warning-message i {
	font-size:18px;
	float:left;
	height:24px;
	line-height:24px;
	padding-right:10px;
}
.j-pro .j-error-message ul,
.j-pro .j-success-message ul,
.j-pro .j-info-message ul,
.j-pro .j-warning-message ul { margin:0; }

.j-pro span.j-error-view,
.j-pro span.j-success-view,
.j-pro span.j-warning-view,
.j-pro span.j-info-view {
	display:block;
	font-size:14px;
	height:14px;
	line-height:14px;
	margin-top:5px;
	padding:0 2px;
}
.j-pro span.j-hint {
	display:block;
	font-size:13px;
	color:inherit;
	height:13px;
	line-height:13px;
	margin-top:5px;
	padding:0 2px;
}

/* Disabled state
=============================== */
.j-pro .j-widget.j-disabled-view,
.j-pro .j-input.j-disabled-view,
.j-pro .j-select.j-disabled-view,
.j-pro .j-checkbox.j-disabled-view,
.j-pro .j-radio.j-disabled-view,
.j-pro .j-checkbox-toggle.j-disabled-view,
.j-pro .j-radio-toggle.j-disabled-view,
.j-pro .j-primary-btn.j-disabled-view,
.j-pro .j-secondary-btn.j-disabled-view,
.j-pro .j-file-button.j-disabled-view { cursor:default; opacity:.5; }

.j-pro .j-widget.j-disabled-view .j-addon-btn i,
.j-pro .j-input.j-disabled-view input[type="file"] { cursor:default; }

.j-pro .j-widget.j-disabled-view input,
.j-pro .j-input.j-disabled-view input,
.j-pro .j-input.j-disabled-view textarea,
.j-pro .j-select.j-disabled-view select { border-color:rgba(0,0,0,.12) !important; }

.j-pro .j-checkbox.j-disabled-view i,
.j-pro .j-radio.j-disabled-view i,
.j-pro .j-checkbox-toggle.j-disabled-view i,
.j-pro .j-radio-toggle.j-disabled-view i { border-color:rgba(0,0,0,.26) !important; }

.j-pro .j-primary-btn.j-disabled-view,
.j-pro .j-secondary-btn.j-disabled-view,
.j-pro .j-disabled-view .j-file-button { background:#303f9f; }

.j-pro .j-widget.j-disabled-view .j-addon-btn,
.j-pro .j-widget.j-disabled-view .j-addon-btn:hover,
.j-pro .j-widget.j-disabled-view .j-addon-btn:focus { cursor:default; background:#303f9f; }

/* Error state
=============================== */
.j-pro .j-error-view .j-checkbox i,
.j-pro .j-error-view .j-radio i,
.j-pro .j-error-view .j-checkbox-toggle i,
.j-pro .j-error-view .j-radio-toggle i,
.j-pro .j-error-view input,
.j-pro .j-error-view select,
.j-pro .j-error-view textarea { background:#ffebee !important; }

.j-pro .j-select.j-error-view i {
	background-color:#ffebee;
	-webkit-box-shadow:0 0 0 12px #ffebee;
	-moz-box-shadow:0 0 0 12px #ffebee;
	-o-box-shadow:0 0 0 12px #ffebee;
	box-shadow:0 0 0 12px #ffebee;
}
.j-pro .j-error-view .j-icon-left,
.j-pro .j-error-view .j-icon-right { border-color:#e57373; }

.j-pro .j-error-view .j-icon-left,
.j-pro .j-error-view .j-icon-right,
.j-pro span.j-error-view,
.j-pro .j-error-message i { color:#b71c1c; }

.j-pro .j-error-message { background:#ffebee; border-color:#b71c1c; color:#b71c1c; }

.j-pro .j-tooltip.j-error-view {
	background:#ffebee !important;
	border:2px solid #b71c1c;
	color:#b71c1c;
	opacity:1;
	height: auto;
	padding:5px 10px;
	z-index:5;
}
.j-pro .j-error-view.j-tooltip-left-top { bottom:100%; margin-bottom:8px; left:0; right:auto; }

.j-pro .j-error-view.j-tooltip-left-top:before { border-color:#c02129 transparent !important; bottom:-9px; left:13px; }

.j-pro .j-error-view.j-tooltip-right-top { bottom:100%; margin-bottom:8px; left:auto; right:0; }

.j-pro .j-error-view.j-tooltip-right-top:before { border-color:#c02129 transparent !important; bottom:-9px; }

.j-pro .j-error-view.j-tooltip-left-bottom { margin-top:8px; top:100%; left:0; right:auto; }

.j-pro .j-error-view.j-tooltip-left-bottom:before { border-color:#c02129 transparent !important; top:-9px; left:13px; }

.j-pro .j-error-view.j-tooltip-right-bottom { margin-top:8px; top:100%; left:auto; right:0; }

.j-pro .j-error-view.j-tooltip-right-bottom:before { border-color:#c02129 transparent !important; top:-9px; }

.j-pro .j-error-view.j-tooltip-right-side { margin-left:8px; top:4px; left:100%; white-space:nowrap; }

.j-pro .j-error-view.j-tooltip-right-side:before { border-color:transparent #c02129 !important; top:6px; left:-9px; }

.j-pro .j-error-view.j-tooltip-left-side { margin-right:8px; top:4px; left:auto; right:100%; white-space:nowrap; }

.j-pro .j-error-view.j-tooltip-left-side:before { border-color:transparent #c02129 !important; top:6px; right:-9px; }

/* Success state
=============================== */
.j-pro .j-success-view .j-checkbox i,
.j-pro .j-success-view .j-radio i,
.j-pro .j-success-view .j-checkbox-toggle i,
.j-pro .j-success-view .j-radio-toggle i,
.j-pro .j-success-view input,
.j-pro .j-success-view select,
.j-pro .j-success-view textarea { background:#e8f5e9 !important; }

.j-pro .j-select.j-success-view i {
	background-color:#e8f5e9;
	-webkit-box-shadow:0 0 0 12px #e8f5e9;
	-moz-box-shadow:0 0 0 12px #e8f5e9;
	-o-box-shadow:0 0 0 12px #e8f5e9;
	box-shadow:0 0 0 12px #e8f5e9;
}
.j-pro .j-success-view .j-icon-left,
.j-pro .j-success-view .j-icon-right { border-color:#81c784; }

.j-pro .j-success-view .j-icon-left,
.j-pro .j-success-view .j-icon-right,
.j-pro span.j-success-view,
.j-pro .j-success-message i { color:#1b5e20; }

.j-pro .j-success-message { background:#e8f5e9; border-color:#1b5e20; color:#1b5e20; }

/* Warning state
=============================== */
.j-pro .j-warning-view .j-checkbox i,
.j-pro .j-warning-view .j-radio i,
.j-pro .j-warning-view .j-checkbox-toggle i,
.j-pro .j-warning-view .j-radio-toggle i,
.j-pro .j-warning-view input,
.j-pro .j-warning-view select,
.j-pro .j-warning-view textarea { background:#fff8e1 !important; }

.j-pro .j-select.j-warning-view i {
	background-color:#fff8e1;
	-webkit-box-shadow:0 0 0 12px #fff8e1;
	-moz-box-shadow:0 0 0 12px #fff8e1;
	-o-box-shadow:0 0 0 12px #fff8e1;
	box-shadow:0 0 0 12px #fff8e1;
}
.j-pro .j-warning-view .j-icon-left,
.j-pro .j-warning-view .j-icon-right { border-color:#f9a825; }

.j-pro .j-warning-view .j-icon-left,
.j-pro .j-warning-view .j-icon-right,
.j-pro span.j-warning-view,
.j-pro .j-warning-message i { color:#f57f17; }

.j-pro .j-warning-message { background:#fff8e1; border-color:#f57f17; color:#f57f17; }

/* Info state
=============================== */
.j-pro .j-info-view .j-checkbox i,
.j-pro .j-info-view .j-radio i,
.j-pro .j-info-view .j-checkbox-toggle i,
.j-pro .j-info-view .j-radio-toggle i,
.j-pro .j-info-view input,
.j-pro .j-info-view select,
.j-pro .j-info-view textarea { background:#e1f5fe !important; }

.j-pro .j-select.j-info-view i {
	background-color:#e1f5fe;
	-webkit-box-shadow:0 0 0 12px #e1f5fe;
	-moz-box-shadow:0 0 0 12px #e1f5fe;
	-o-box-shadow:0 0 0 12px #e1f5fe;
	box-shadow:0 0 0 12px #e1f5fe;
}
.j-pro .j-info-view .j-icon-left,
.j-pro .j-info-view .j-icon-right { border-color:#0288d1; }

.j-pro .j-info-view .j-icon-left,
.j-pro .j-info-view .j-icon-right,
.j-pro span.j-info-view,
.j-pro .j-info-message i { color:#01579b; }

.j-pro .j-info-message { background:#e1f5fe; border-color:#01579b; color:#01579b; }

/* Ratings
==================================== */
.j-pro .j-rating-group { color:rgba(0,0,0,.87); height:30px; line-height:30px; margin-bottom:4px; }

.j-pro .j-rating-group:last-child { margin-bottom:0; }

.j-pro .j-rating-group .j-label { float:left; font-size:16px; height:30px; line-height:30px; margin-bottom:0; }

.j-pro .j-rating-group .j-ratings { float:right; height:30px; line-height:30px; }

.j-pro .j-ratings input { left:-9999px; position:absolute; }

.j-pro .j-ratings input + label {
	color:rgba(0,0,0,.26);
	cursor:pointer;
	font-size:20px;
	float:right;
	padding:0 2px;
	-webkit-transition:color.2s;
	-moz-transition:color.2s;
	-ms-transition:color.2s;
	-o-transition:color.2s;
	transition:color.2s;
}
.j-pro .j-ratings input + label:hover,
.j-pro .j-ratings input + label:hover ~ label,
.j-pro .j-ratings input:checked + label,
.j-pro .j-ratings input:checked + label ~ label { color:#303f9f; }

/* Datapicker and Timepicker
=============================== */
.ui-datepicker {
	background-color:#fff;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	border:1px solid rgba(0,0,0,.26);
	-webkit-box-shadow:0 0 2px rgba(0,0,0,.5);
	-moz-box-shadow:0 0 2px rgba(0,0,0,.5);
	-o-box-shadow:0 0 2px rgba(0,0,0,.5);
	box-shadow:0 0 2px rgba(0,0,0,.5);
	color:rgba(0,0,0,.54);
	display:none;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	text-align:center;
	padding:10px 0;
	width:240px;
	z-index:1100 !important;
}
.ui-datepicker-header {
	background-color:#f0f0f0;
	line-height:1.5;
	margin:-2px 0 12px;
	padding:10px;
	position:relative;
}
.ui-datepicker-prev,
.ui-datepicker-next {
	cursor:pointer;
	display:block;
	font-size:18px;
	height:30px;
	position:absolute;
	text-decoration:none;
	top:6px;
	width:30px;
}
.ui-datepicker-prev { border-right:1px solid; left:0; }

.ui-datepicker-next { border-left:1px solid; right:0; }

.ui-datepicker-calendar { border-collapse:collapse; line-height:1.5; width:100%; }

.ui-datepicker-calendar th span { color:rgba(0,0,0,.26); font-weight:lighter; }

.ui-datepicker-calendar a,
.ui-datepicker-calendar span {
	color:rgba(0,0,0,.54);
	display:block;
	font-size:16px;
	margin:0 auto;
	text-decoration:none;
	width:28px;
}
.ui-datepicker-calendar a:hover,
.ui-datepicker-calendar .ui-state-active { background-color:#e0e0e0; }

.ui-datepicker-today a { outline:1px solid rgba(0,0,0,.54); }

.ui-datepicker-inline {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	border:2px solid rgba(0,0,0,.12);
	-webkit-box-shadow:none;
	-moz-box-shadow:none;
	-o-box-shadow:none;
	box-shadow:none;
	width:100%;
}
.ui-state-disabled span { color:rgba(0,0,0,.26); }

.ui-timepicker-div .ui-widget-header { background-color:#f0f0f0; margin-bottom:8px; padding:10px 0; }

.ui-timepicker-div dl { text-align:left; }

.ui-timepicker-div dl dt { float:left; clear:left; padding:0 0 0 5px; }

.ui-timepicker-div td { font-size:90%; }

.ui-tpicker-grid-label { background:none; border:none; margin:0; padding:0; }

.ui-timepicker-rtl{ direction:rtl; }

.ui-timepicker-rtl dl { text-align:right; padding:0 5px 0 0; }

.ui-timepicker-rtl dl dt{ float:right; clear:right; }

.ui-timepicker-rtl dl dd { margin:0 40% 10px 10px; }

.ui-timepicker-div { font-size:15px; }

.ui-timepicker-div dl {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	border-top:1px solid rgba(0,0,0,.26);
	padding:16px 5px;
	margin:16px 0 0;
}
.ui-timepicker-div .ui_tpicker_time { margin:0 10px 10px 40%; }

.ui-timepicker-div .ui_tpicker_hour,
.ui-timepicker-div .ui_tpicker_minute { margin:16px 10px 10px 40%; }

.ui-datepicker-buttonpane { border-top:1px solid rgba(0,0,0,.26); }

.ui-datepicker-buttonpane button {
	background:#e0e0e0;
	border:none;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	color:rgba(0,0,0,.56);
	cursor:pointer;
	font:14px 'Open Sans',Helvetica,Arial,sans-serif;
	padding:5px 10px;
	margin:10px 5px 0;
	-webkit-transition:all.15s;
	-moz-transition:all.15s;
	-ms-transition:all.15s;
	-o-transition:all.15s;
	transition:all.15s;
	outline:none;
}
.ui-datepicker-buttonpane button:hover { background:#d6d6d6; color:rgba(0,0,0,.87); }

/* jQuery Slider
=============================== */
.ui-slider { position:relative; }

.ui-slider .ui-slider-range {
	border:none;
	display:block;
	font-size:11px;
	position:absolute;
	overflow:hidden;
	z-index:1;
}
.ui-slider .ui-slider-handle {
	background-color:#e0e0e0;
	border:1px solid rgba(0,0,0,.36);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	cursor:pointer;
	height:16px;
	position:absolute;
	outline:none;
	left:-5px;
	width:16px;
	z-index:2;
}
.ui-slider-horizontal { height:7px; }

.ui-slider-vertical { height:100px; width:7px; }

.ui-slider-horizontal .ui-slider-handle { top:-5px; margin-left:-10px; }

.ui-slider-horizontal .ui-slider-range { top:0; height:100%; }

.ui-slider-horizontal .ui-slider-range-min { left:0; }

.ui-slider-horizontal .ui-slider-range-max { right:0; }

.ui-slider-vertical .ui-slider-range-min { bottom:0; }

.ui-slider-vertical .ui-slider-range { left:0; width:100%; }

.ui-slider.ui-widget-content {
	background-color:#fff;
	border:2px solid #e0e0e0;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
}
.ui-slider-vertical .ui-widget-header,
.ui-slider-horizontal .ui-widget-header { background-color:#f0f0f0; }

.j-pro .j-slider-group {
	font:15px 'Open Sans',Helvetica,Arial,sans-serif;
	height:48px;
	line-height:48px;
	padding:0 2px;
	margin-bottom:5px;
	white-space:nowrap;
}
.j-pro .j-slider-group label { display:inline-block; color:rgba(0,0,0,.87); padding:0 4px; }

/* Multistep form
=============================== */
.j-pro fieldset {
	border:none;
	outline:none;
	margin:0;
	padding:0;
	position:absolute;
	opacity:0;
	left:-9999px;
	top:0;
	-webkit-transform:translateY(-4%);
	-moz-transform:translateY(-4%);
	-ms-transform:translateY(-4%);
	-o-transform:translateY(-4%);
	transform:translateY(-4%);
	-webkit-transition:opacity.3s, -webkit-transform.3s;
	-moz-transition:opacity.3s, -moz-transform.3s;
	-ms-transition:opacity.3s, -ms-transform.3s;
	-o-transition:opacity.3s, -o-transform.3s;
	transition:opacity.3s, transform.3s;
}
.j-pro .j-steps {
	border:1px solid rgba(0,0,0,.12);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	margin-bottom:25px;
	text-align:center;
	-webkit-transition:all.3s;
	-moz-transition:all.3s;
	-ms-transition:all.3s;
	-o-transition:all.3s;
	transition:all.3s;
}
.j-pro .j-active-fieldset {
	left:0;
	position:relative;
	opacity:1;
	-webkit-transform:translateY(0);
	-moz-transform:translateY(0);
	-ms-transform:translateY(0);
	-o-transform:translateY(0);
	transform:translateY(0);
}
.j-pro fieldset .j-unit,
.j-pro fieldset .j-row { display:none; }

.j-pro .j-active-fieldset .j-unit,
.j-pro .j-active-fieldset .j-row { display:block; }

.j-pro .j-steps p { color:rgba(0,0,0,.56); font-size:16px; height:36px; line-height:36px; margin:0; padding:0; }

.j-pro .j-steps span { color:rgba(0,0,0,.56); font-size:13px; height:13px; line-height:13px; }

.j-pro .j-active-step .j-steps p { color:rgba(0,0,0,.87); }

.j-pro .j-active-step .j-steps { background-color:#e8eaf6; border:1px solid #303f9f; }

.j-pro .j-passed-step .j-steps { background-color:#e8eaf6; border:1px solid #e8eaf6; }

.j-pro.j-multistep .j-input textarea:focus { height:112px; }

/* Modal form
=============================== */
.j-modal-form {
	background-color:rgba(103,119,129,.5);
	bottom:0;
	height:100%;
	left:0;
	opacity:0;
	overflow-y:scroll;
	position:fixed;
	right:0;
	top:0;
	visibility:hidden;
	width:100%;
	z-index:1040;
}
.j-modal-form.j-modal-visible { opacity:1; visibility:visible; }

.j-modal-scroll { overflow:hidden; }

.j-pro .j-modal-close {
	background-color:rgba(0,0,0,.3);
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
	cursor:pointer;
	position:absolute;
	right:8px;
	top:11px;
	-webkit-transition:background-color.15s;
	-moz-transition:background-color.15s;
	-ms-transition:background-color.15s;
	-o-transition:background-color.15s;
	transition:background-color.15s;
}
.j-pro .j-modal-close:hover,
.j-pro .j-modal-close:focus { background-color:rgba(0,0,0,.6); }

.j-pro .j-modal-close i { display:block; height:22px; width:23px; }

.j-pro .j-modal-close i:before,
.j-pro .j-modal-close i:after {
	background-color:#fff;
	content:'';
	height:3px;
	position:absolute;
	right:1px;
	top:10px;
	width:21px;
}
.j-pro .j-modal-close i:before{
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);
	-ms-transform:rotate(45deg);
	-o-transform:rotate(45deg);
	transform:rotate(45deg);
}
.j-pro .j-modal-close i:after{
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
}

/* Grid layout 
=============================== */
.j-pro [class*="j-span"] {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	float:left;
	padding-left:10px;
	padding-right:10px;
	position:relative;
}
.j-pro .j-span1 { width:8.3333%; }
.j-pro .j-span2 { width:16.6666%; }
.j-pro .j-span3 { width:25%; }
.j-pro .j-span4 { width:33.3333%; }
.j-pro .j-span5 { width:41.6666%; }
.j-pro .j-span6 { width:50%; }
.j-pro .j-span7 { width:58.3333%; }
.j-pro .j-span8 { width:66.6666%; }
.j-pro .j-span9 { width:75%; }
.j-pro .j-span10 { width:83.3333%; }
.j-pro .j-span11 { width:91.6666%; }
.j-pro .j-span12 { width:100%; }

.j-pro .j-offset1 { margin-left:8.3333%; }
.j-pro .j-offset2 { margin-left:16.6666%; }
.j-pro .j-offset3 { margin-left:25%; }
.j-pro .j-offset4 { margin-left:33.3333%; }
.j-pro .j-offset5 { margin-left:41.6666%; }
.j-pro .j-offset6 { margin-left:50%; }
.j-pro .j-offset7 { margin-left:58.3333%; }
.j-pro .j-offset8 { margin-left:66.6666%; }
.j-pro .j-offset9 { margin-left:75%; }
.j-pro .j-offset10 { margin-left:83.3333%; }
.j-pro .j-offset11 { margin-left:91.6666%; }
.j-pro .j-offset12 { margin-left:100%; }

.j-pro .j-row{ margin:0 -10px; }

.j-pro .j-row:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

/* Responsiveness
==================================== */
/* Wrapper-640 */
@media all and (max-width:620px) {

	.j-wrapper-640 .j-pro [class*="j-span"] { margin-right:0; width:100%; }

	.j-wrapper-640 .j-pro [class*="j-offset"] { margin-left:0; }

	.j-wrapper-640 .j-pro .j-label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }

	.j-wrapper-640 .j-pro .j-radio:last-child,
	.j-wrapper-640 .j-pro .j-checkbox:last-child,
	.j-wrapper-640 .j-pro .j-radio-toggle:last-child,
	.j-wrapper-640 .j-pro .j-checkbox-toggle:last-child { margin-bottom:4px; }
}

/* Wrapper-400 */
@media all and (max-width:380px) {

	.j-wrapper-400 .j-pro [class*="j-span"] { margin-right:0; width:100%; }

	.j-wrapper-400 [class*="j-offset"] { margin-left:0;	}

	.j-wrapper-400 .j-pro .j-label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }

	.j-wrapper-400 .j-pro .j-radio:last-child,
	.j-wrapper-400 .j-pro .j-checkbox:last-child,
	.j-wrapper-400 .j-pro .j-radio-toggle:last-child,
	.j-wrapper-400 .j-pro .j-checkbox-toggle:last-child { margin-bottom:4px; }
}

/* jQuery UI Autocomplete
=============================== */
.ui-autocomplete {
	background:#fff;
	border-color:rgba(0,0,0,.12);
	border-style:none solid solid;
	border-width:0 2px 2px;
	-webkit-border-radius:0 0 3px 3px;
	-moz-border-radius:0 0 3px 3px;
	-o-border-radius:0 0 3px 3px;
	border-radius:0 0 3px 3px;
	cursor:default;
	display:block;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	left:0;
	list-style:none;
	margin:0;
	max-height:110px;
	outline:none;
	overflow-x:hidden;
	overflow-y:auto;
	padding:0;
	position:absolute;
	top:0;
	z-index:10000;
}
.ui-autocomplete .ui-menu { position:absolute; }

.ui-autocomplete .ui-menu-item {
	background:#fff;
	border-bottom:1px solid rgba(0,0,0,.12);
	cursor:pointer;
	margin:0;
	min-height:0;
	padding:8px 15px;
	position:relative;
}
.ui-autocomplete .ui-menu-item:hover { background:rgba(0,0,0,.06); }

.ui-helper-hidden-accessible { position:absolute; left:-999em; }

/* Tabs
=============================== */
.j-tabs-section,
.j-tabs-container input[type="radio"] { display:none; }

#tab1:checked ~ #tabs-section-1,
#tab2:checked ~ #tabs-section-2,
#tab3:checked ~ #tabs-section-3 { display:block; }

.j-tabs-container .j-tabs-label {
	background:#fff;
	border-top:2px solid rgba(0,0,0,.34);
	-webkit-border-radius:2px 2px 0 0;
	-moz-border-radius:2px 2px 0 0;
	-o-border-radius:2px 2px 0 0;
	border-radius:2px 2px 0 0;
	display:inline-block;
	margin:0 0 -3px;
	font:14px 'Open Sans',Helvetica,Arial,sans-serif;
	padding:11px 13px 13px;
	-webkit-box-shadow:0 2px 2px rgba(0,0,0,.6);
	-moz-box-shadow:0 2px 2px rgba(0,0,0,.6);
	-o-box-shadow:0 2px 2px rgba(0,0,0,.6);
	box-shadow:0 2px 2px rgba(0,0,0,.6);
	opacity:.95;
	-webkit-transition:border-top.2s;
	-moz-transition:border-top.2s;
	-ms-transition:border-top.2s;
	-o-transition:border-top.2s;
	transition:border-top.2s;
}
.j-tabs-container .j-tabs-label i,
.j-tabs-container .j-tabs-label span { padding:1px; }

.j-tabs-container .j-tabs-label i { font-size:16px; }

.j-tabs-container .j-tabs-label i,
.j-tabs-container .j-tabs-label span,
.j-tabs-container input[type="radio"]:checked + .j-tabs-label i { color:rgba(0,0,0,.54); }

.j-tabs-container .j-tabs-label:hover { cursor:pointer; }

.j-tabs-container input[type="radio"]:checked + .j-tabs-label { border-top:2px solid rgba(0,0,0,.87); opacity:1; }

.j-tabs-container input[type="radio"]:checked + .j-tabs-label span { color:rgba(0,0,0,.87); }

@media all and (max-width: 430px) {
	.j-tabs-container .j-tabs-label { font-size:0; }
	.j-tabs-container .j-tabs-label i { font-size:16px; height:18px; }
	.j-tabs-container input[type="radio"]:checked + .j-tabs-label i { color:rgba(0,0,0,.87); }
}

/* Image checkbox & Image radio
=============================== */
.j-pro .j-radio-block,
.j-pro .j-checkbox-block {
	color:rgba(0,0,0,.87);
	cursor:pointer;
	font-size:15px;
	margin-bottom:8px;
	position:relative;
}
.j-pro .j-radio-block:last-child,
.j-pro .j-checkbox-block:last-child { margin-bottom:0; }

.j-pro .j-radio-block input,
.j-pro .j-checkbox-block input { position:absolute; left:-9999px; }

.j-pro .j-radio-block i,
.j-pro .j-checkbox-block i {
	display:block;
	height:18px;
	outline:none;
	position:absolute;
	top:5px;
	left:5px;
	width:18px;
	z-index:10;
}
.j-pro .j-checkbox-block i {
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
}
.j-pro .j-checkbox-block i:after {
	border-width:0 0 3px 3px;
	border-bottom:solid rgb(48,63,159);
	border-left:solid rgb(48,63,159);
	content:"";
	height:5px;
	top:3px;
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
	left:3px;
	opacity:0;
	position:absolute;
	width:10px;
}
.j-pro .j-radio-block i,
.j-pro .j-radio-block i:after {
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	-o-border-radius:50%;
	border-radius:50%;
}
.j-pro .j-radio-block i:after {
	background-color:rgba(48,63,159,.9);
	content:"";
	height:8px;
	top:5px;
	left:5px;
	opacity:0;
	position:absolute;
	width:8px;
}
.j-pro .j-radio-block input:checked + i,
.j-pro .j-checkbox-block input:checked + i { color:rgba(48,63,159,.9); border:2px solid rgba(48,63,159,.9); }

.j-pro .j-radio-block input:checked + i:after,
.j-pro .j-checkbox-block input:checked + i:after { opacity:1; }

.j-pro .j-checkbox-block span,
.j-pro .j-radio-block span { display:block; }

.j-pro .j-checkbox-block .j-block-image,
.j-pro .j-radio-block .j-block-image { position:relative; border:0;}

.j-pro .j-checkbox-block .j-block-content img,
.j-pro .j-radio-block .j-block-content img { height:auto; display:block; width:100%; }

.j-pro .j-checkbox-block .j-block-content:hover,
.j-pro .j-radio-block .j-block-content:hover,
.j-pro .j-checkbox-block input:checked ~ .j-block-content,
.j-pro .j-radio-block input:checked ~ .j-block-content {
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
}
.j-pro .j-checkbox-block .j-block-content:hover,
.j-pro .j-radio-block .j-block-content:hover { background:rgba(232,234,246,.5); }

.j-pro .j-checkbox-block input:checked ~ .j-block-content,
.j-pro .j-radio-block input:checked ~ .j-block-content { background:#e8eaf6; }

.j-pro .j-checkbox-block .j-block-text-title,
.j-pro .j-radio-block .j-block-text-title { color:rgba(0,0,0,.87); font-size:17px; margin:5px 0; }

.j-pro .j-checkbox-block .j-block-text-desc,
.j-pro .j-radio-block .j-block-text-desc {
	color:rgba(0,0,0,.54);
	font-size:13px;
	padding-right:5px;
	line-height:1.2;
	margin:5px 0 10px;
	word-spacing:3px;
}
.j-pro .j-checkbox-block .j-block-image-title,
.j-pro .j-radio-block .j-block-image-title,
.j-pro .j-checkbox-block .j-block-content,
.j-pro .j-radio-block .j-block-content {
	-webkit-transition:background .2s;
	-moz-transition:background .2s;
	-ms-transition:background .2s;
	-o-transition:background .2s;
	transition:background .2s;
}
.j-pro .j-checkbox-block .j-block-image-title,
.j-pro .j-radio-block .j-block-image-title {
	background:rgba(0,0,0,.34);
	bottom:0;
	color:#fff;
	font-size:13px;
	padding:10px;
	position:absolute;
	left:0;
	width:100%;
	z-index:20;
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
}
.j-pro .j-checkbox-block .j-block-content:hover .j-block-image-title,
.j-pro .j-radio-block .j-block-content:hover .j-block-image-title { background:rgba(0,0,0,.57); }

.j-pro .j-checkbox-block input:checked ~ .j-block-content .j-block-image-title,
.j-pro .j-radio-block input:checked ~ .j-block-content .j-block-image-title { background:rgba(0,0,0,.77); }

/* "Thank you" block
=============================*/
.j-final-block {
	background-color:#f9fafd;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	-webkit-box-shadow:0 2px 5px rgba(0,0,0,.6);
	-moz-box-shadow:0 2px 5px rgba(0,0,0,.6);
	-o-box-shadow:0 2px 5px rgba(0,0,0,.6);
	box-shadow:0 2px 5px rgba(0,0,0,.6);
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	color:rgba(0,0,0,.87);
	font:600 16px 'Open Sans',Helvetica,Arial,sans-serif;
	line-height:1;
	position:relative;
	padding:25px 20px;
}
.j-final-block .j-close {
	background-color:rgba(0,0,0,.3);
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
	cursor:pointer;
	position:absolute;
	right:8px;
	top:11px;
	-webkit-transition:background-color.15s;
	-moz-transition:background-color.15s;
	-ms-transition:background-color.15s;
	-o-transition:background-color.15s;
	transition:background-color.15s;
}
.j-final-block .j-close:hover,
.j-final-block .j-close:focus { background-color:rgba(0,0,0,.6); }

.j-final-block .j-close i { display:block; height:22px; width:23px; }

.j-final-block .j-close i:before,
.j-final-block .j-close i:after {
	background-color:#fff;
	content:'';
	height:3px;
	position:absolute;
	right:1px;
	top:10px;
	width:21px;
}
.j-final-block .j-close i:before{
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);
	-ms-transform:rotate(45deg);
	-o-transform:rotate(45deg);
	transform:rotate(45deg);
}
.j-final-block .j-close i:after{
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
}

/* Form details & total price
=============================== */
.j-pro .j-total-data,
.j-pro .j-calculation-data { font:14px/25px 'Open Sans',Helvetica,Arial,sans-serif; }

.j-pro .j-total-data p { display:block; margin:0; padding:5px 0; }

.j-pro .j-total-data span,
.j-pro .j-calculation-data span { color:rgba(0,0,0,.87); padding:0 4px; font-size:16px; }

.j-pro .j-calculation-data p { display:block; margin:0; padding:0; }

.j-pro .j-calculation-data {
	background:#c4df9b;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	text-align:center;
	padding:8px 0;
}

/* Bootstrap compatibility
=============================== */
.j-pro .j-radio,
.j-pro .j-checkbox,
.j-pro .j-radio-toggle,
.j-pro .j-checkbox-toggle { margin-top:0; }

.j-pro .j-label {
	padding:0;
	-webkit-border-radius:0;
	-moz-border-radius:0;
	-o-border-radius:0;
	border-radius:0;
}
.j-pro .j-radio,
.j-pro .j-checkbox,
.j-pro .j-radio-toggle,
.j-pro .j-checkbox-toggle,
.j-pro .j-radio *,
.j-pro .j-checkbox *,
.j-pro .j-radio-toggle *,
.j-pro .j-checkbox-toggle *,
.j-pro .j-radio i:after,
.j-pro .j-checkbox i:after,
.j-pro .j-radio-toggle i:after,
.j-pro .j-checkbox-toggle i:after,
.j-pro .j-radio i:before,
.j-pro .j-checkbox i:before,
.j-pro .j-radio-toggle i:before,
.j-pro .j-checkbox-toggle i:before {
	-webkit-box-sizing:content-box;
	-moz-box-sizing:content-box;
	box-sizing:content-box;
}