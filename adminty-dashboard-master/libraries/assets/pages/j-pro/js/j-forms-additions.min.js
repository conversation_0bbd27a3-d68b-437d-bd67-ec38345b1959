$(document).ready(function(){$("#show-pass").on("change",function(){$("#show-pass").is(":checked")?$("#password").attr("type","text"):$("#password").attr("type","password")}),$("#check-enable-input").on("change",function(){$("#check-enable-input").is(":checked")?$("#enable-input").attr("disabled",!1).parent().removeClass("disabled-view"):$("#enable-input").attr("disabled",!0).parent().addClass("disabled-view")}),$("#check-enable-button").on("change",function(){$("#check-enable-button").is(":checked")?$("#enable-button").attr("disabled",!1).removeClass("disabled-view"):$("#enable-button").attr("disabled",!0).addClass("disabled-view")}),$models=[],$colors=[],$models.VO=["V70","XC60","XC90"],$models.VW=["Golf","Polo","Scirocco","Touareg"],$models.BMW=["M6","X5","Z3"],$colors.V70=["black","white"],$colors.XC60=["green","orange"],$colors.XC90=["brown","red"],$colors.Golf=["purple","yellow"],$colors.Polo=["grey","indigo"],$colors.Scirocco=["blue","teal","cyan"],$colors.Touareg=["red","black","orange","brown"],$colors.M6=["orange","brown","red","indigo","cyan"],$colors.X5=["white","green","cyan"],$colors.Z3=["teal","purple","cyan"],$("#car").change(function(){if($("#car-model option").length&&$("#car-model option:gt(0)").remove(),$("#car-model-color option").length&&$("#car-model-color option:gt(0)").remove(),$("#car option:selected").each(function(){$car_val=$(this).val()}),$car=$models[$car_val])for($i=0;$i<$car.length;$i++)$opt='<option value="'+$car[$i]+'">'+$car[$i]+"</option>",$("#car-model").append($opt)}),$("#car-model").change(function(){if($("#car-model-color option").length&&$("#car-model-color option:gt(0)").remove(),$("#car-model option:selected").each(function(){$car_model_val=$(this).val()}),$color=$colors[$car_model_val])for($i=0;$i<$color.length;$i++)$opt='<option value="'+$color[$i]+'">'+$color[$i]+"</option>",$("#car-model-color").append($opt)}),$("#show-elements-checkbox").on("change",function(){$("#show-elements-checkbox").is(":checked")?$(".hidden-elements").removeClass("hidden"):$(".hidden-elements").addClass("hidden")}),$("#show-elements-select").change(function(){var e="",o=$("#field-1"),a=$("#field-2");$("#show-elements-select option:selected").each(function(){e=$(this).val()}),"none"==e&&(o.hasClass("hidden")||o.addClass("hidden"),a.hasClass("hidden")||a.addClass("hidden")),"field-1"==e&&(o.hasClass("hidden")&&o.removeClass("hidden"),a.hasClass("hidden")||a.addClass("hidden")),"field-2"==e&&(a.hasClass("hidden")&&a.removeClass("hidden"),o.hasClass("hidden")||o.addClass("hidden")),"field-1-2"==e&&(o.hasClass("hidden")&&o.removeClass("hidden"),a.hasClass("hidden")&&a.removeClass("hidden"))}).change(),$("#subscribe").change(function(){$("#subscribe").is(":checked")?($(".subscribe").removeClass("disabled-view"),$('.subscribe input[type="checkbox"]').removeAttr("disabled")):($(".subscribe").addClass("disabled-view"),$('.subscribe input[type="checkbox"]').attr("disabled","true").removeAttr("checked"))}).change()});