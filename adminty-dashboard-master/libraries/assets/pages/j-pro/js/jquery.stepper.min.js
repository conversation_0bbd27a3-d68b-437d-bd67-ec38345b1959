/*
Numeric Stepper jQuery plugin
Licensed under MIT:
Copyright (c) <PERSON>
*/
(function($){$.fn.stepper=function(options){var _defaults={type:'float',floatPrecission:2,ui:true,allowWheel:true,allowArrows:true,arrowStep:1,wheelStep:1,limit:[null,null],preventWheelAcceleration:true,onStep:null,onWheel:null,onArrow:null,onButton:null,onKeyUp:null};return $(this).each(function(){var $data=$(this).data();delete $data.stepper;var _options=$.extend({},_defaults,options,$data),$this=$(this),$wrap=$('<div class="stepper"/>');if($this.data('stepper'))return;$wrap.insertAfter($this);$this.appendTo($wrap);$this.stepper=(function(){return{limit:_limit,decimalRound:_decimal_round,onStep:function(callback){_options.onStep=callback},onWheel:function(callback){_options.onWheel=callback},onArrow:function(callback){_options.onArrow=callback},onButton:function(callback){_options.onButton=callback},onKeyUp:function(callback){_options.onKeyUp=callback}}})();$this.data('stepper',$this.stepper);if(_options.ui){var $btnWrap=$('<div class="stepper-wrapper"/>').appendTo($wrap),$btnUp=$('<i class="stepper-arrow up"></i>').appendTo($btnWrap),$btnDown=$('<i class="stepper-arrow down"></i>').appendTo($btnWrap);$this.css('margin',0);var stepInterval;$btnUp.mousedown(function(e){e.preventDefault();var val=_step(_options.arrowStep);_evt('Button',[val,true])});$btnDown.mousedown(function(e){e.preventDefault();var val=_step(-_options.arrowStep);_evt('Button',[val,false])});$(document).mouseup(function(){clearInterval(stepInterval)})}if(_options.allowWheel){$wrap.bind('DOMMouseScroll',_handleWheel);$wrap.bind('mousewheel',_handleWheel)}$wrap.keydown(function(e){var key=e.which,val=$this.val();if(_options.allowArrows)switch(key){case 38:val=_step(_options.arrowStep);_evt('Arrow',[val,true]);break;case 40:val=_step(-_options.arrowStep);_evt('Arrow',[val,false]);break}if((key<37&&key>40)||(key>57&&key<91)||(key>105&&key!=110&&key!=190))e.preventDefault();if(_options.type=='float'&&$.inArray(key,[110,190])!=-1&&val.indexOf('.')!=-1)e.preventDefault()}).keyup(function(e){_evt('KeyUp',[$this.val()])});function _handleWheel(e){e.preventDefault();var d,evt=e.originalEvent;if(evt.wheelDelta)d=evt.wheelDelta/120;else if(evt.detail)d=-evt.detail/3;if(d){if(_options.preventWheelAcceleration)d=d<0?-1:1;var val=_step(_options.wheelStep*d);_evt('Wheel',[val,d>0])}}function _step(step){if(!$this.val())$this.val(0);var typeCast=_options.type=='int'?parseInt:parseFloat,val=_limit(typeCast($this.val())+step);$this.val(val);_evt('Step',[val,step>0]);return val}function _evt(name,args){var callback=_options['on'+name];if(typeof callback=='function')callback.apply($this,args)}function _limit(num){var min=_options.limit[0],max=_options.limit[1];if(min!==null&&num<min)num=min;else if(max!==null&&num>max)num=max;return _decimal_round(num)}function _decimal_round(num,precission){if(typeof precission=='undefined')precission=_options.floatPrecission;var pow=Math.pow(10,precission);num=Math.round(num*pow)/pow;return num}})}})(jQuery);