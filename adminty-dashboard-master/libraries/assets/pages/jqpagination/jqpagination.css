.jqpagination {
  display: inline-block;
  border: 1px solid #CDCDCD;
  border-radius: 3px; }

.jqpagination a {
  display: block;
  float: left;
  width: 20px;
  height: 30px;
  outline: none;
  border-right: 1px solid #CDCDCD;
  border-left: 1px solid #CDCDCD;
  color: #555555;
  vertical-align: middle;
  text-align: center;
  text-decoration: none;
  font-weight: bold;
  font-size: 16px;
  font-family: Times, 'Times New Roman', Georgia, Palatino;
  /* ATTN: need a better font stack */
  background-color: #f3f3f3;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f3f3f3), color-stop(100%, lightgrey));
  background-image: -webkit-linear-gradient(#f3f3f3, lightgrey);
  background-image: linear-gradient(#f3f3f3, lightgrey); }
  .jqpagination a:hover, .jqpagination a:focus, .jqpagination a:active {
    background-color: #cecece;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #e4e4e4), color-stop(100%, #cecece));
    background-image: -webkit-linear-gradient(#e4e4e4, #cecece);
    background-image: linear-gradient(#e4e4e4, #cecece); }
  .jqpagination a.disabled, .jqpagination a.disabled:hover, .jqpagination a.disabled:focus, .jqpagination a.disabled:active {
    background-color: #f3f3f3;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f3f3f3), color-stop(100%, lightgrey));
    background-image: -webkit-linear-gradient(#f3f3f3, lightgrey);
    background-image: linear-gradient(#f3f3f3, lightgrey);
    color: #A8A8A8;
    cursor: default; }

.jqpagination a:first-child {
  border: none;
  border-radius: 2px 0 0 2px; }

.jqpagination a:last-child {
  border: none;
  border-radius: 0 2px 2px 0; }

.jqpagination input {
  float: left;
  margin:5px 0 ;
  padding: 0;
  width: 120px;
  height: 20px;
  outline: none;
  border: none;
  vertical-align: middle;
  text-align: center; }

/* gigantic class for demo purposes */
.gigantic.jqpagination {
  margin: 30px 0; }

.gigantic.jqpagination a {
  height: 60px;
  width: 60px;
  font-size: 50px;
  line-height: 50px; }

.gigantic.jqpagination input {
  width: 300px;
  height: 60px;
  font-size: 30px; }

/* log element for demo purposes */
.log {
  display: none;
  background-color: #EDEDED;
  border: 1px solid #B4B4B4;
  height: 300px;
  width: 524px;
  overflow: auto;
  margin-left: 0;
  list-style: none;
  padding: 10px; }
  .log li {
    margin-top: 0;
    margin-bottom: 5px; }
