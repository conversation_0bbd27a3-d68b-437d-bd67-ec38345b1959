html {
	height: 100%;
}

body {
	font-family: 'Source Sans Pro', sans-serif;
	font-size: 14px;
}


/* Form styles */
#msform {
	width: 400px;
	margin: 50px auto;
	text-align: center;
	position: relative;
}
#msform fieldset {
	background: #fff;
	border: 0 none;
	border-radius: 5px;
	box-shadow: 0 0 30px 1px rgba(0, 0, 0, 0.1);
	padding: 20px;
	box-sizing: border-box;
	width: 80%;
	margin: 0 auto;
	left:0%;
	right: 0%;
	position: relative;
}

/* Hide all except first fieldset */
#msform fieldset:not(:first-of-type) {
	display: none;
}
img.logo {
	max-width: 120px;
	margin-bottom: 20px;
}
#msform p {
	color: #8b9ab0;
	font-size: 12px;
}

/* Inputs */


/* Buttons */
#msform .action-button {
	width: 49%;
	text-transform: uppercase;
	background: #e74c3c;
	font-weight: bold;
	color: white;
	border: 1px solid transparent;
	border-radius: 3px;
	cursor: pointer;
	padding: 12px 5px;
	margin: 10px 0;
	font-size: 16px;
	display: inline-block;
	-webkit-transition: all 0.2s;
	-moz-transition: all 0.2s;
	transition: all 0.2s;
}
#msform .previous.action-button {
	background: #fff;
	border: 1px solid #7bbdf3;
	color: #7bbdf3;
}

/* Headings */
.fs-title {
	font-size: 26px;
	font-weight: 200;
	color: #434a54;
	margin-bottom: 20px;
}
.fs-subtitle {
	font-weight: 400;
	color: #434a54;
	margin-bottom: 20px;
	line-height: 1.4
}
#msform #progressbar li{
	font-size: 13px;
	text-transform: capitalize;
}
/* Progressbar */
#progressbar {
	margin-bottom: 30px;
	overflow: hidden;
	/*CSS counters to number the steps*/
	counter-reset: step;
}
#progressbar li {
	list-style-type: none;
	color: #8b9ab0;
	text-transform: uppercase;
	font-size: 9px;
	width: 33.33%;
	float: left;
	position: relative;
}
#progressbar li.active {
	color: #e74c3c;
}
#progressbar li:before {
	content: counter(step);
	counter-increment: step;
	width: 20px;
	line-height: 20px;
	display: block;
	font-size: 10px;
	color: #333;
	background: white;
	border-radius: 3em;
	margin: 0 auto 5px auto;
}

/* Progressbar connectors */
#progressbar li:after {
	content: '';
	width: 100%;
	height: 2px;
	background: white;
	position: absolute;
	left: -50%;
	top: 9px;
	z-index: -1;
}
#progressbar li:first-child:after {
	/* connector not needed before the first step */
	content: none;
}

/* Marking active/completed steps green */
/*The number of the step and the connector before it = green*/
#progressbar li.active:before,  #progressbar li.active:after{
	background: #e74c3c;
	color: white;
}
