/** Trumbowyg v2.0.0-beta.3 - A lightweight WYSIWYG editor - alex-d.github.io/Trumbowyg - License MIT - Author : <PERSON> (Alex-D) / alex-d.fr */
jQuery.trumbowyg={langs:{en:{viewHTML:"View HTML",formatting:"Formatting",p:"Paragraph",blockquote:"Quote",code:"Code",header:"Header",bold:"Bold",italic:"Italic",strikethrough:"Stroke",underline:"Underline",strong:"Strong",em:"Emphasis",del:"Deleted",unorderedList:"Unordered list",orderedList:"Ordered list",insertImage:"Insert Image",insertVideo:"Insert Video",link:"Link",createLink:"Insert link",unlink:"Remove link",justifyLeft:"Align Left",justifyCenter:"Align Center",justifyRight:"Align Right",justifyFull:"Align Justify",horizontalRule:"Insert horizontal rule",removeformat:"Remove format",fullscreen:"fullscreen",close:"Close",submit:"Confirm",reset:"Cancel",required:"Required",description:"Description",title:"Title",text:"Text",target:"Target"}},opts:{},btnsGrps:{design:["bold","italic","underline","strikethrough"],semantic:["strong","em","del"],justify:["justifyLeft","justifyCenter","justifyRight","justifyFull"],lists:["unorderedList","orderedList"]}},function(e,t,n,o,r){"use strict";o.fn.trumbowyg=function(e,t){if(e===Object(e)||!e)return this.each(function(){o(this).data("trumbowyg")||o(this).data("trumbowyg",new i(this,e))});if(1===this.length)try{var n=o(this).data("trumbowyg");switch(e){case"openModal":return n.openModal(t.title,t.content);case"closeModal":return n.closeModal();case"openModalInsert":return n.openModalInsert(t.title,t.fields,t.callback);case"saveSelection":return n.saveSelection();case"getSelection":return n.selection;case"getSelectedText":return n.getSelectedText();case"restoreSelection":return n.restoreSelection();case"destroy":return n.destroy();case"empty":return n.empty();case"lang":return n.lang;case"html":return n.html(t)}}catch(r){}return!1};var i=function(e,t){var r=this;r.doc=e.ownerDocument||n,r.$ta=o(e),r.$c=o(e),t=o.extend(!0,{},t,o.trumbowyg.opts),r.lang="undefined"==typeof t.lang||"undefined"==typeof o.trumbowyg.langs[t.lang]?o.trumbowyg.langs.en:o.extend(!0,{},o.trumbowyg.langs.en,o.trumbowyg.langs[t.lang]);var i=r.lang.header;r.o=o.extend(!0,{},{lang:"en",dir:"ltr",closable:!1,fullscreenable:!0,fixedBtnPane:!1,fixedFullWidth:!1,autogrow:!1,prefix:"trumbowyg-",semantic:!0,resetCss:!1,removeformatPasted:!1,btns:["viewHTML","|","formatting","|","btnGrp-design","|","link","|","insertImage","|","btnGrp-justify","|","btnGrp-lists","|","horizontalRule","|","removeformat"],btnsAdd:[],btnsDef:{viewHTML:{func:"toggle"},p:{func:"formatBlock"},blockquote:{func:"formatBlock"},h1:{func:"formatBlock",title:i+" 1"},h2:{func:"formatBlock",title:i+" 2"},h3:{func:"formatBlock",title:i+" 3"},h4:{func:"formatBlock",title:i+" 4"},bold:{key:"B"},italic:{key:"I"},underline:{},strikethrough:{},strong:{func:"bold",key:"B"},em:{func:"italic",key:"I"},del:{func:"strikethrough"},createLink:{key:"K"},unlink:{},insertImage:{},justifyLeft:{},justifyCenter:{},justifyRight:{},justifyFull:{},unorderedList:{func:"insertUnorderedList"},orderedList:{func:"insertOrderedList"},horizontalRule:{func:"insertHorizontalRule"},removeformat:{},formatting:{dropdown:["p","blockquote","h1","h2","h3","h4"]},link:{dropdown:["createLink","unlink"]}},blockLevelElements:["br","p","div","ul","ol","table","img","address","article","aside","audio","blockquote","canvas","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","noscript","output","pre","section","tfoot","video"]},t),t.btns?r.o.btns=t.btns:r.o.semantic&&(r.o.btns[4]="btnGrp-semantic"),r.keys=[],r.init()};i.prototype={init:function(){var e=this;e.height=e.$ta.height(),e.buildEditor(),e.buildBtnPane(),e.fixedBtnPaneEvents(),e.buildOverlay()},buildEditor:function(){var e=this,r=e.o.prefix,i="";e.$box=o("<div/>",{"class":r+"box "+r+"editor-visible "+r+e.o.lang+" trumbowyg"}),e.isTextarea=e.$ta.is("textarea"),e.isTextarea?(i=e.$ta.val(),e.$ed=o("<div/>"),e.$box.insertAfter(e.$ta).append(e.$ed,e.$ta)):(e.$ed=e.$ta,i=e.$ed.html(),e.$ta=o("<textarea/>",{name:e.$ta.attr("id"),height:e.height}).val(i),e.$box.insertAfter(e.$ed).append(e.$ta,e.$ed),e.syncCode()),e.$ta.addClass(r+"textarea").attr("tabindex",-1),e.$ed.addClass(r+"editor").attr({contenteditable:!0,dir:e.lang._dir||e.o.dir}).html(i),e.$c.is("[placeholder]")&&e.$ed.attr("placeholder",e.$c.attr("placeholder")),e.o.resetCss&&e.$ed.addClass(r+"reset-css"),e.o.autogrow||e.$ta.add(e.$ed).css({height:e.height,overflow:"auto"}),e.o.semantic&&e.semanticCode(),e._ctrl=!1,e.$ed.on("dblclick","img",function(){var t=o(this);return e.openModalInsert(e.lang.insertImage,{url:{label:"URL",value:t.attr("src"),required:!0},alt:{label:e.lang.description,value:t.attr("alt")}},function(e){return t.attr({src:e.url,alt:e.alt})}),!1}).on("keydown",function(t){if(e._composition=229===t.which,t.ctrlKey){e._ctrl=!0;var n=e.keys[String.fromCharCode(t.which).toUpperCase()];try{return e.execCmd(n.func,n.param),!1}catch(t){}}}).on("keyup",function(t){e._ctrl||17===t.which||e._composition||(e.semanticCode(!1,13===t.which),e.$c.trigger("tbwchange")),setTimeout(function(){e._ctrl=!1},200)}).on("focus blur",function(t){e.$c.trigger("tbw"+t.type)}).on("paste",function(o){if(e.o.removeformatPasted){o.preventDefault();try{var r=t.clipboardData.getData("Text");try{e.doc.selection.createRange().pasteHTML(r)}catch(i){e.doc.getSelection().getRangeAt(0).insertNode(n.createTextNode(r))}}catch(i){e.execCmd("insertText",(o.originalEvent||o).clipboardData.getData("text/plain"))}}setTimeout(function(){e.o.semantic?e.semanticCode(!1,!0):e.syncCode(),e.$c.trigger("tbwpaste",o)},0)}),e.$ta.on("keyup paste",function(){e.$c.trigger("tbwchange")}),o(e.doc).on("keydown",function(t){return 27===t.which?(e.closeModal(),!1):void 0})},buildBtnPane:function(){var e=this,n=e.o.prefix;if(e.o.btns!==!1){e.$btnPane=o("<ul/>",{"class":n+"button-pane"}),o.each(e.o.btns.concat(e.o.btnsAdd),function(t,i){try{var a=i.split("btnGrp-");a[1]!==r&&(i=o.trumbowyg.btnsGrps[a[1]])}catch(s){}o.isArray(i)||(i=[i]),o.each(i,function(t,r){try{var i=o("<li/>");"|"===r?i.addClass(n+"separator"):e.isSupportedBtn(r)&&i.append(e.buildBtn(r)),e.$btnPane.append(i)}catch(a){}})});var i=o("<li/>",{"class":n+"not-disable "+n+"buttons-right"});e.o.fullscreenable&&i.append(e.buildRightBtn("fullscreen").on("click",function(){var r=n+"fullscreen";e.$box.toggleClass(r),e.$box.hasClass(r)?(o("body").addClass(n+"body-fullscreen"),o.each([e.$ta,e.$ed],function(){o(this).css({height:"calc(100% - 35px)",overflow:"auto"})}),e.$btnPane.css("width","100%")):(o("body").removeClass(n+"body-fullscreen"),e.$box.removeAttr("style"),e.o.autogrow||o.each([e.$ta,e.$ed],function(){o(this).css("height",e.height)})),o(t).trigger("scroll")})),e.o.closable&&i.append(e.buildRightBtn("close").on("click",function(){e.$box.hasClass(n+"fullscreen")&&o("body").css("overflow","auto"),e.destroy(),e.$c.trigger("tbwclose")})),i.not(":empty")&&e.$btnPane.append(i),e.$box.prepend(e.$btnPane)}},buildBtn:function(e){var t=this,n=t.o.prefix,r=t.o.btnsDef[e],i=r.dropdown,a=t.lang[e]||e,s=o("<button/>",{type:"button","class":n+e+"-button"+(r.ico?" "+n+r.ico+"-button":""),text:r.text||r.title||a,title:r.title||r.text||a+(r.key?" (Ctrl + "+r.key+")":""),tabindex:-1,mousedown:function(){return(!i||o("."+e+"-"+n+"dropdown",t.$box).is(":hidden"))&&o("body",t.doc).trigger("mousedown"),!t.$btnPane.hasClass(n+"disable")||o(this).hasClass(n+"active")||o(this).parent().hasClass(n+"not-disable")?(t.execCmd((i?"dropdown":!1)||r.func||e,r.param||e),!1):!1}});if(i){s.addClass(n+"open-dropdown");var l=n+"dropdown",c=o("<div/>",{"class":e+"-"+l+" "+l+" "+n+"fixed-top"});o.each(i,function(e,n){t.o.btnsDef[n]&&t.isSupportedBtn(n)&&c.append(t.buildSubBtn(n))}),t.$box.append(c.hide())}else r.key&&(t.keys[r.key]={func:r.func||e,param:r.param||e});return s},buildSubBtn:function(e){var t=this,n=t.o.btnsDef[e];return n.key&&(t.keys[n.key]={func:n.func||e,param:n.param||e}),o("<button/>",{type:"button","class":t.o.prefix+e+"-dropdown-button"+(n.ico?" "+t.o.prefix+n.ico+"-button":""),text:n.text||n.title||t.lang[e]||e,title:n.key?" (Ctrl + "+n.key+")":null,style:n.style||null,mousedown:function(){return o("body",t.doc).trigger("mousedown"),t.execCmd(n.func||e,n.param||e),!1}})},buildRightBtn:function(e){var t=this.lang[e];return o("<button/>",{type:"button","class":this.o.prefix+e+"-button",title:t,text:t,tabindex:-1})},isSupportedBtn:function(e){try{return this.o.btnsDef[e].isSupported()}catch(t){}return!0},buildOverlay:function(){var e=this;return e.$overlay=o("<div/>",{"class":e.o.prefix+"overlay"}).css({top:e.$btnPane.outerHeight(),height:e.$ed.outerHeight()+1+"px"}).appendTo(e.$box),e.$overlay},showOverlay:function(){var e=this;o(t).trigger("scroll"),e.$overlay.fadeIn(200),e.$box.addClass(e.o.prefix+"box-blur")},hideOverlay:function(){var e=this;e.$overlay.fadeOut(50),e.$box.removeClass(e.o.prefix+"box-blur")},fixedBtnPaneEvents:function(){var e=this,n=e.o.fixedFullWidth,r=e.$box;e.o.fixedBtnPane&&(e.isFixed=!1,o(t).on("scroll resize",function(){if(r){e.syncCode();var i=o(t).scrollTop(),a=r.offset().top+1,s=e.$btnPane,l=s.outerHeight();i-a>0&&i-a-e.height<0?(e.isFixed||(e.isFixed=!0,s.css({position:"fixed",top:0,left:n?"0":"auto",zIndex:7}),o([e.$ta,e.$ed]).css({marginTop:s.height()})),s.css({width:n?"100%":r.width()-1+"px"}),o("."+e.o.prefix+"fixed-top",r).css({position:n?"fixed":"absolute",top:n?l:l+(i-a)+"px",zIndex:15})):e.isFixed&&(e.isFixed=!1,s.removeAttr("style"),o([e.$ta,e.$ed]).css({marginTop:0}),o("."+e.o.prefix+"fixed-top",r).css({position:"absolute",top:l}))}}))},destroy:function(){var e=this,t=e.o.prefix,n=e.height,o=e.html();e.isTextarea?e.$box.after(e.$ta.css({height:n}).val(o).removeClass(t+"textarea").show()):e.$box.after(e.$ed.css({height:n}).removeClass(t+"editor").removeAttr("contenteditable").html(o).show()),e.$box.remove(),e.$c.removeData("trumbowyg")},empty:function(){this.$ta.val(""),this.syncCode(!0)},toggle:function(){var e=this,t=e.o.prefix;e.semanticCode(!1,!0),e.$box.toggleClass(t+"editor-hidden "+t+"editor-visible"),e.$btnPane.toggleClass(t+"disable"),o("."+t+"viewHTML-button",e.$btnPane).toggleClass(t+"active"),e.$box.hasClass(t+"editor-visible")?e.$ta.attr("tabindex",-1):e.$ta.removeAttr("tabindex")},dropdown:function(e){var n=this,r=n.doc,i=n.o.prefix,a=o("."+e+"-"+i+"dropdown",n.$box),s=o("."+i+e+"-button",n.$btnPane);if(a.is(":hidden")){var l=s.offset().left;s.addClass(i+"active"),a.css({position:"absolute",top:n.$btnPane.outerHeight(),left:n.o.fixedFullWidth&&n.isFixed?l+"px":l-n.$btnPane.offset().left+"px"}).show(),o(t).trigger("scroll"),o("body",r).on("mousedown",function(){o("."+i+"dropdown",r).hide(),o("."+i+"active",r).removeClass(i+"active"),o("body",r).off("mousedown")})}else o("body",r).trigger("mousedown")},html:function(e){var t=this;return e?(t.$ta.val(e),t.syncCode(!0),t):t.$ta.val()},syncCode:function(e){var t=this;!e&&t.$ed.is(":visible")?(t.$ta.val(t.$ed.html()),t.$c.trigger("tbwchange")):t.$ed.html(t.$ta.val()),t.o.autogrow&&(t.height=t.$ed.height(),t.height!=t.$ta.css("height")&&(t.$ta.css({height:t.height}),t.$c.trigger("tbwresize")))},semanticCode:function(e,t){var n=this;if(n.syncCode(e),n.saveSelection(),n.o.semantic){if(n.semanticTag("b","strong"),n.semanticTag("i","em"),n.semanticTag("strike","del"),t){var r=n.o.blockLevelElements.join(", "),i=":not("+r+")";n.$ed.contents().filter(function(){return 3===this.nodeType&&o.trim(this.nodeValue).length>0}).wrap("<span data-trumbowyg-textnode/>");var a=function(e){if(0!==e.length){var t=e.nextUntil(r+", br").andSelf().wrapAll("<p/>").parent();t.next("br").remove();var n=t.nextAll(i).first();n.length&&a(n)}};a(n.$ed.children(i).first()),n.semanticTag("div","p",!0),n.$ed.find("p").filter(function(){return n.selection&&this===n.selection.startContainer?!1:0===o(this).text().trim().length&&0===o(this).children().not("br, span").length}).contents().unwrap(),o("[data-trumbowyg-textnode]",n.$ed).contents().unwrap(),n.$ed.find("p:empty").replaceWith("<br/>")}n.restoreSelection(),n.$ta.val(n.$ed.html())}},semanticTag:function(e,t,n){o(e,this.$ed).each(function(){var e=o(this);e.wrap("<"+t+"/>"),n&&o.each(e.prop("attributes"),function(){e.parent().attr(this.name,this.value)}),e.contents().unwrap()})},createLink:function(){var e=this;e.saveSelection(),e.openModalInsert(e.lang.createLink,{url:{label:"URL",required:!0},title:{label:e.lang.title},text:{label:e.lang.text,value:e.getSelectedText()},target:{label:e.lang.target}},function(t){var n=o(['<a href="',t.url,'">',t.text,"</a>"].join(""));return t.title.length>0&&n.attr("title",t.title),t.target.length>0&&n.attr("target",t.target),e.selection.deleteContents(),e.selection.insertNode(n.get(0)),e.restoreSelection(),!0})},insertImage:function(){var e=this;e.saveSelection(),e.openModalInsert(e.lang.insertImage,{url:{label:"URL",required:!0},alt:{label:e.lang.description,value:e.getSelectedText()}},function(t){return e.execCmd("insertImage",t.url),o('img[src="'+t.url+'"]:not([alt])',e.$box).attr("alt",t.alt),!0})},execCmd:function(t,n){var o=this;"dropdown"!=t&&o.$ed.focus();try{o[t](n)}catch(r){try{t(n,o)}catch(i){"insertHorizontalRule"==t?n=null:"formatBlock"==t&&(-1!==e.userAgent.indexOf("MSIE")||e.appVersion.indexOf("Trident/")>0)&&(n="<"+n+">"),o.doc.execCommand(t,!1,n)}}"dropdown"!=t&&o.syncCode()},openModal:function(e,n){var r=this,i=r.o.prefix;if(o("."+i+"modal-box",r.$box).length>0)return!1;r.saveSelection(),r.showOverlay(),r.$btnPane.addClass(i+"disable");var a=o("<div/>",{"class":i+"modal "+i+"fixed-top"}).css({top:r.$btnPane.height()+1+"px"}).appendTo(r.$box);r.$overlay.one("click",function(){return a.trigger(i+"cancel"),!1});var s=o("<form/>",{action:"",html:n}).on("submit",function(){return a.trigger(i+"confirm"),!1}).on("reset",function(){return a.trigger(i+"cancel"),!1}),l=o("<div/>",{"class":i+"modal-box",html:s}).css({top:"-"+r.$btnPane.outerHeight()+"px",opacity:0}).appendTo(a).animate({top:0,opacity:1},100);return o("<span/>",{text:e,"class":i+"modal-title"}).prependTo(l),a.height(l.outerHeight()+10),o("input:first",l).focus(),r.buildModalBtn("submit",l),r.buildModalBtn("reset",l),o(t).trigger("scroll"),a},buildModalBtn:function(e,t){var n=this,r=n.o.prefix;return o("<button/>",{"class":r+"modal-button "+r+"modal-"+e,type:e,text:n.lang[e]||e}).appendTo(o("form",t))},closeModal:function(){var e=this,t=e.o.prefix;e.$btnPane.removeClass(t+"disable"),e.$overlay.off();var n=o("."+t+"modal-box",e.$box);n.animate({top:"-"+n.height()},100,function(){n.parent().remove(),e.hideOverlay()}),e.restoreSelection()},openModalInsert:function(e,t,n){var r=this,i=r.o.prefix,a=r.lang,s="";for(var l in t){var c=t[l],d=c.label,u=c.name?c.name:l;s+='<label><input type="'+(c.type||"text")+'" name="'+u+'" value="'+(c.value||"")+'"><span class="'+i+'input-infos"><span>'+(d?a[d]?a[d]:d:a[l]?a[l]:l)+"</span></span></label>"}return r.openModal(e,s).on(i+"confirm",function(){var e=o("form",o(this)),a=!0,s={};for(var l in t){var c=o('input[name="'+l+'"]',e);s[l]=o.trim(c.val()),t[l].required&&""===s[l]?(a=!1,r.addErrorOnModalField(c,r.lang.required)):t[l].pattern&&!t[l].pattern.test(s[l])&&(a=!1,r.addErrorOnModalField(c,t[l].patternError))}a&&(r.restoreSelection(),n(s,t)&&(r.syncCode(),r.closeModal(),o(this).off(i+"confirm")))}).one(i+"cancel",function(){o(this).off(i+"confirm"),r.closeModal()})},addErrorOnModalField:function(e,t){var n=this.o.prefix,r=e.parent();e.on("change keyup",function(){r.removeClass(n+"input-error")}),r.addClass(n+"input-error").find("input+span").append(o("<span/>",{"class":n+"msg-error",text:t}))},saveSelection:function(){var e=this,n=e.doc.selection;if(e.selection=null,t.getSelection){var o=t.getSelection();o.getRangeAt&&o.rangeCount&&(e.selection=o.getRangeAt(0))}else n&&n.createRange&&(e.selection=n.createRange())},restoreSelection:function(){var e=this,n=e.selection;if(n)if(t.getSelection){var o=t.getSelection();o.removeAllRanges(),o.addRange(n)}else e.doc.selection&&n.select&&n.select()},getSelectedText:function(){var e=this.selection;return e.text!==r?e.text:e+""}}}(navigator,window,document,jQuery);