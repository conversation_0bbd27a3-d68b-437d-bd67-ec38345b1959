$social-name: facebook,twiter,dribble,pinterest,youtube,googleplus,instagram,viber,amazon;
$social-color: #3B5997,#42C0FB,#EC4A89,#BF2131,#E0291D,#C73E2E,#AA7C62,#7B519D,#000;
$color-name: c-blue,c-pink,c-green,c-yellow,c-orenge,c-lite-green;
$color-color: #01a9ac,#fe5d70,#0ac282,#fe9365,#FE8A7D,#01a9ac;
$color-blue: #01a9ac;
$color-pink: #fe5d70;
$color-green: #0ac282;
$color-yellow: #fe9365;
$color-orenge: #FE8A7D;
$color-lite-green: #01a9ac;
$txt-lite-color: #919aa3;
$card-bg-inverce: #353C4E;
$facebook: #3B5997;
$twitter: #42C0FB;
$dribble: #EC4A89;
@each $value in $social-name {
    $i: index($social-name, $value);

    .bg-#{$value} {
        background-color: nth($social-color,$i);
    }

    .text-#{$value} {
        color: nth($social-color,$i);
    }
}
@each $value in $color-name {
    $i: index($color-name, $value);

    .bg-#{$value} {
        background: linear-gradient(to right, nth($color-color,$i),lighten(nth($color-color,$i), 10%));
    }

    .bg-simple-#{$value} {
        background: nth($color-color,$i);
    }

    .text-#{$value} {
        color: nth($color-color,$i);
    }
}

.update-card {
    color: #fff;

    .card-footer {
        background-color: transparent;
        border-top: 1px solid #fff;
    }
}

.statustic-progress-card {
    .progress {
        height: 5px;
    }
}
// user radial card start

.user-radial-card {
    text-align: center;

    > div {
        margin: 0 auto;
        display: block;

        img {
            box-shadow: 0 0 0 10px #fff;
        }
    }

    p {
        margin-bottom: 0;
    }
}
// user radial card end

.user-card-full {
    overflow: hidden;

    .user-profile {
        border-radius: 5px 0 0 5px;
    }

    .social-link {
        li {
            display: inline-block;

            a {
                i {
                    color: $light-color;
                }
                font-size: 20px;
                margin: 0 10px 0 0;
                transition: all 0.3s ease-in-out;

                i {
                    &.facebook {
                        &:hover {
                            color: $facebook;
                        }
                    }

                    &.twitter {
                        &:hover {
                            color: $twitter;
                        }
                    }

                    &.instagram {
                        &:hover {
                            color: $instagram;
                        }
                    }

                    &.youtube {
                        &:hover {
                            color: $youtube;
                        }
                    }
                }
            }
        }
    }
}
// statustic card start

.statustic-card {
    .card-block {
        position: relative;

        .progress {
            position: absolute;
            bottom: 0;
            width: 100%;
            left: 0;
            height: 5px;
            overflow: visible;

            .progress-bar {
                position: relative;

                &:before {
                    content: "";
                    height: 5px;
                    width: 5px;
                    border-radius: 50%;
                    position: absolute;
                    right: 0;
                    background: inherit;
                }

                &.bg-c-blue:before {
                    animation: blue-blink-bar 0.5s linear infinite;
                }

                &.bg-c-green:before {
                    animation: green-blink-bar 0.5s linear infinite;
                }

                &.bg-c-pink:before {
                    animation: pink-blink-bar 0.5s linear infinite;
                }

                &.bg-c-yellow:before {
                    animation: yellow-blink-bar 0.5s linear infinite;
                }
            }
        }
    }
}
@keyframes blue-blink-bar {
    0% {
        box-shadow: 0 0 0 0 transparentize($color-blue,0.9);
    }

    50% {
        box-shadow: 0 0 0 6px transparentize($color-blue,0.7);
    }
}
@keyframes green-blink-bar {
    0% {
        box-shadow: 0 0 0 0 transparentize($color-green,0.9);
    }

    50% {
        box-shadow: 0 0 0 6px transparentize($color-green,0.7);
    }
}
@keyframes pink-blink-bar {
    0% {
        box-shadow: 0 0 0 0 transparentize($color-pink,0.9);
    }

    50% {
        box-shadow: 0 0 0 6px transparentize($color-pink,0.7);
    }
}
@keyframes yellow-blink-bar {
    0% {
        box-shadow: 0 0 0 0 transparentize($color-yellow,0.9);
    }

    50% {
        box-shadow: 0 0 0 6px transparentize($color-yellow,0.7);
    }
}
// statustic card end

.widget-statstic-card {
    position: relative;
    overflow: hidden;

    .st-icon {
        color: #fff;
        font-size: 23px;
        padding: 40px 40px 20px 20px;
        border-radius: 50%;
        position: absolute;
        top: -30px;
        right: -30px;
        transition: all 0.3s ease-in-out;
    }

    h2 {
        font-weight: 600;
        display: inline-block;
    }

    span {
        border-radius: 30px;
        padding: 5px 20px;
        color: #fff;
        font-weight: 600;
    }

    &:hover {
        .st-icon {
            font-size: 50px;
        }
    }
}

.social-card {
    color: #fff;
    overflow: hidden;

    .social-icon {
        background-color: #fff;
        border-radius: 5px;
        text-align: center;
        padding: 22px 23px;
    }

    .download-icon {
        color: #fff;
        background-color: rgba(255,255,255,0.5);
        position: absolute;
        height: 125px;
        width: 125px;
        right: -125px;
        font-size: 35px;
        padding: 40px 22px;
        border-radius: 50%;
        transition: all 0.3s ease-in-out;
    }

    &:hover {
        .download-icon {
            right: -60px;
        }
    }
}

.widget-visitor-card {
    overflow: hidden;
    padding: 10px 0;

    i {
        color: #fff;
        font-size: 80px;
        position: absolute;
        bottom: -10px;
        opacity: 0.3;
        left: -10px;
        transform: rotate(15deg);
        transition: all 0.3s ease-in-out;
    }

    &:hover {
        i {
            transform: rotate(0deg) scale(1.4);
            opacity: 0.5;
        }
    }
}

.app-design {
    .btn {
        padding: 5px 8px;
        font-size: 12px;
        font-weight: 600;
        border-radius: 5px;
    }

    .team-section {
        img {
            width: 35px;
            border-radius: 5px;
        }
    }

    .progress-box {
        p {
            margin-bottom: 0;
        }

        .progress {
            width: calc(100% - 80px);
            height: 8px;
            text-align: center;
            margin: 0 auto;
            background-color: #E5E5E5;
            border-radius: 30px;
            position: relative;
            overflow: inherit;

            .progress-bar {
                border-radius: 30px;

                label {
                    position: absolute;
                    top: -24px;
                    right: 0;
                    color: #222;
                    font-weight: 600;
                    font-size: 13px;
                }
            }
        }
    }
}

.widget-card-1 {
    margin-top: 20px;
    text-align: right;

    .card1-icon {
        width: 60px;
        height: 60px;
        position: absolute;
        top: -15px;
        font-size: 35px;
        border-radius: 8px;
        display: flex;
        color: #fff;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease-in-out;
    }

    .card-block > span {
        color: $txt-lite-color;
    }

    h4 {
        font-weight: 400;
        margin-top: 10px;
    }

    &:hover {
        .card1-icon {
            top: -25px;
        }
    }
}

.user-widget-card {
    margin-top: 10px;
    margin-bottom: 40px;
    text-align: center;
    color: #fff;

    .card-block {
        padding: 1.95rem 1.25rem 0.85rem;
    }

    .card1-icon {
        width: 40px;
        height: 40px;
        position: absolute;
        top: -15px;
        left: calc(50% - 20px);
        font-size: 20px;
        border-radius: 50%;
        box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #fff;
    }

    h4 {
        font-weight: 400;
        margin-top: 10px;
    }

    .more-info {
        background: #fff;
        padding: 5px 15px;
        border-radius: 15px;
        position: absolute;
        left: calc(50% - 45px);
        box-shadow: 0 1px 14px -5px #000;
    }
}

.order-card {
    color: #fff;
    overflow: hidden;

    .card-icon {
        position: absolute;
        right: -17px;
        font-size: 100px;
        top: 20px;
        opacity: 0.5;
    }
}

.user-card2 {
    .risk-rate {
        display: inline-block;
        margin: 0 auto;

        span {
            width: 130px;
            height: 130px;
            border-radius: 50%;
            border: 6px dashed #d6d6d6;
            border-top-color: $color-yellow;
            border-bottom-color: transparent;
            padding: 45px;
            display: block;
            position: relative;

            &:after {
                content: "";
                width: 90px;
                height: 90px;
                background-color: transparentize($color-yellow,0.5);
                border-radius: 50%;
                position: absolute;
                top: 15px;
                left: 15px;
                z-index: 1;
            }

            b {
                font-size: 20px;
                color: #fff;
                z-index: 2;
                position: relative;
            }
        }
    }
}
.table-card {
    .card-block {
        padding-left: 0;
        padding-right: 0;
        padding-top: 0;

        .table {
            > thead > tr > th {
                border-top: 0;
            }

            .chk-option {
                vertical-align: middle;
                width: 30px;
                display: inline-block;

                .checkbox-fade,
                .checkbox-fade .cr,
                .checkbox-zoom,
                .checkbox-zoom .cr {
                    margin-right: 0;
                }
            }

            label {
                margin-bottom: 0;
            }

            tr {
                td,
                th {
                    &:first-child {
                        padding-left: 20px;
                    }

                    &:last-child {
                        padding-right: 20px;
                    }
                }
            }
        }
    }
}
#app-sale1, #app-sale2, #app-sale3, #app-sale4 {
     height: 46px !important;
     width: 100px !important;
}

.user-activity-card {
    .u-img {
        position: relative;

        .cover-img {
            width: 40px;
            height: 40px;
        }

        .profile-img {
            width: 20px;
            height: 20px;
            position: absolute;
            bottom: -5px;
            right: -5px;
        }
    }
}
.wather-card {
    overflow: hidden;

    .nature-card {
        position: relative;
        background: linear-gradient(lighten($color-yellow,5%) 60%, $color-yellow 100%);
        overflow: hidden;

        .main-img {
            width: 100%;
            @media only screen and (min-width: 1400px) {
                height: 260px;
            }
        }

        .bottom-img {
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
        }

        .snow1,
        .snow2 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }

        .snow1 {
            animation: sparcle 2.1s linear infinite;
        }

        .snow2 {
            animation: sparcle 2.1s linear infinite;
            animation-delay: 1.15s;
        }

        .nature-cont {
            position: absolute;
            top: 20px;
            text-align: center;
            width: 100%;
        }
    }
}
@keyframes sparcle {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }

    20% {
        opacity: 1;
        transform: translateY(-5px);
    }

    50% {
        opacity: 1;
        transform: translateY(0px);
    }

    80% {
        opacity: 1;
        transform: translateY(5px);
    }

    100% {
        opacity: 0;
        transform: translateY(10px);
    }
}

.user-card {
    .usre-image {
        position: relative;
        display: inline-block;
        margin: 0 auto;

        img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 1px solid $color-blue;
            padding: 4px;
        }

        &:before {
            content: "\e83f";
            font-family: 'feather';
            position: absolute;
            bottom: 10px;
            right: 10px;
            color: #fff;
            background-color: $color-green;
            border-radius: 50%;
            padding: 0 3px;
        }

        &:after {
            content: "Permenant";
            position: absolute;
            bottom: -15px;
            left: calc(100% - 27px);
            color: #fff;
            background-color: $color-blue;
            border-radius: 5px;
            padding: 0 3px;
        }
    }

    .btn {
        margin: 10px auto 15px;
    }
}

.feed-card {
    h6 {
        margin-top: 7px;
    }

    .feed-icon {
        color: #fff;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        text-align: center;
        padding: 8px;
    }
}
.latest-update-card {
    .card-block {
        padding-top: 0;

        .latest-update-box {
            position: relative;

            &:after {
                content: "";
                position: absolute;
                background: $theme-border;
                height: 100%;
                width: 1px;
                top: 0;
                left: 110px;
                z-index: 1;
            }

            .update-meta {
                z-index: 2;
                min-width: 160px;

                .update-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    text-align: center;
                    padding: 12px 13px;
                    font-size: 16px;
                    color: #fff;
                    margin-left: 10px;
                }
            }
            @media only screen and (max-width: 575px) {
                &:after {
                    display: none;
                }

                .update-meta {
                    z-index: 2;
                    min-width: 100%;
                    text-align: left !important;
                    margin-bottom: 15px;
                    border-top: 1px solid #f1f1f1;
                    padding-top: 15px;
                }
            }
        }
    }
}

.latest-activity-card {
    .card-block {
        padding-top: 0;

        .latest-update-box {
            position: relative;

            .update-meta {
                z-index: 2;
                min-width: 160px;

                .update-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    text-align: center;
                    padding: 12px 13px;
                    font-size: 16px;
                    color: #fff;
                    margin-left: 10px;
                }
            }
            @media only screen and (max-width: 575px) {
                .update-meta {
                    z-index: 2;
                    min-width: 100%;
                    text-align: left !important;
                    margin-bottom: 15px;
                    border-top: 1px solid #f1f1f1;
                    padding-top: 15px;
                }
            }
        }
    }
    .table td {
        vertical-align: middle;
    }
}
.per-task-card{
    .card-block{
        padding: 0;
    }
    .per-task-block{
        margin-left: 0;
        margin-right: 0;
        >div{
            padding-top: 20px;
            padding-bottom: 20px;
            &:nth-child(odd){
                padding-left: 0;
            }
            &:nth-child(even){
                background-color: transparentize($primary-color, 0.9);
                padding-right: 0;
            }
        }
    }
}

#monthly-graph {
    .amcharts-graph-g2 .amcharts-graph-stroke {
        stroke-linejoin: round;
        stroke-linecap: round;
        animation: am-moving-dashes 1s linear infinite;
    }

    .lastBullet {
        animation: am-pulsating 1s ease-out infinite;
    }

    .amcharts-graph-column-front {
        transition: all 0.3s 0.3s ease-out;
    }

    .amcharts-graph-column-front:hover {
        transition: all 0.3s ease-out;
        fill-opacity: 0.4;
    }

    .amcharts-graph-g3 {
        stroke-linejoin: round;
        stroke-linecap: round;
        stroke-dasharray: 500%;
        animation: am-draw 40s;
    }
}
@keyframes am-moving-dashes {
    100% {
        stroke-dashoffset: -31px;
    }
}
@keyframes am-pulsating {
    0% {
        stroke-opacity: 1;
        stroke-width: 0;
    }

    100% {
        stroke-opacity: 0;
        stroke-width: 50px;
    }
}
@keyframes am-draw {
    0% {
        stroke-dashoffset: 500%;
    }

    100% {
        stroke-dashoffset: 0;
    }
}
//  am chart animation css end
.quater-card{
    .progress{
        height: 4px;
    }
}
.card.social-network {
    p {
        line-height: 1.6;
    }
}
